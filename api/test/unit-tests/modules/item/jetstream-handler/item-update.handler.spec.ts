import { ItemService } from 'src/modules/item/service/item.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { Item } from 'src/modules/item/entity/item.entity';
import { ItemUpdateHandler } from 'src/modules/item/jetstream-handler/item-update.handler';

describe('ItemUpdateHandler', () => {
  let handler: ItemUpdateHandler;
  let itemService: jest.Mocked<ItemService>;

  beforeEach(() => {
    itemService = { createOrUpdateItem: jest.fn() } as jest.Mocked<
      Partial<ItemService>
    >;
    handler = new ItemUpdateHandler(itemService);
  });

  describe('canHandle', () => {
    it('should return true for ITEM and UPDATED', () => {
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: { name: 'item1' } as Item,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-ITEM doctype', () => {
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM_GROUP,
        event: EventTypes.DELETED,
        data: { name: 'item1' } as Item,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-UPDATED event', () => {
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'item1' } as Item,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call itemService.updateItem with payload data', async () => {
      const item: Item = { name: 'item1' } as Item;
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: item,
      };
      await handler.handle(payload);
      expect(itemService.createOrUpdateItem).toHaveBeenCalledWith(item);
    });
  });
});
