import { ItemService } from 'src/modules/item/service/item.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { Item } from 'src/modules/item/entity/item.entity';
import { ItemDeleteHandler } from 'src/modules/item/jetstream-handler/item-delete.handler';

describe('ItemDeleteHandler', () => {
  let handler: ItemDeleteHandler;
  let itemService: jest.Mocked<ItemService>;

  beforeEach(() => {
    itemService = { deleteItem: jest.fn() } as jest.Mocked<
      Partial<ItemService>
    >;
    handler = new ItemDeleteHandler(itemService);
  });

  describe('canHandle', () => {
    it('should return true for ITEM and DELETED', () => {
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'item1' } as Item,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-ITEM doctype', () => {
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM_GROUP,
        event: EventTypes.UPDATED,
        data: { name: 'item1' } as Item,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-DELETED event', () => {
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: { name: 'item1' } as Item,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call itemService.deleteItem with payload data', async () => {
      const item: Item = { name: 'item1' } as Item;
      const payload: JetStreamPayload<Item> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: item,
      };
      await handler.handle(payload);
      expect(itemService.deleteItem).toHaveBeenCalledWith(item);
    });
  });
});
