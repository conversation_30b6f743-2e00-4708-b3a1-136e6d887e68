import { PosProfileService } from 'src/modules/pos-profile/service/pos-profile.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { PosProfile } from 'src/modules/pos-profile/entity/pos-profile.entity';
import { PosProfileDeleteHandler } from 'src/modules/pos-profile/jetstream-handler/pos-profile-delete.handler';

describe('PosProfileDeleteHandler', () => {
  let handler: PosProfileDeleteHandler;
  let posProfileService: jest.Mocked<PosProfileService>;

  beforeEach(() => {
    posProfileService = { deletePosProfile: jest.fn() } as jest.Mocked<
      Partial<PosProfileService>
    >;
    handler = new PosProfileDeleteHandler(posProfileService);
  });

  describe('canHandle', () => {
    it('should return true for POS_PROFILE and DELETED', () => {
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.POS_PROFILE,
        event: EventTypes.DELETED,
        data: { name: 'Borne' } as PosProfile,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-POS_PROFILE doctype', () => {
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'Borne' } as PosProfile,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-DELETED event', () => {
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.POS_PROFILE,
        event: EventTypes.UPDATED,
        data: { name: 'Borne' } as PosProfile,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call posProfileService.deletePosProfile with payload data', async () => {
      const posProfile: PosProfile = { name: 'Borne' } as PosProfile;
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.POS_PROFILE,
        event: EventTypes.DELETED,
        data: posProfile,
      };
      await handler.handle(payload);
      expect(posProfileService.deletePosProfile).toHaveBeenCalledWith(
        posProfile,
      );
    });
  });
});
