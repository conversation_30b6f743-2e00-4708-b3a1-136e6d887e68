import { PosProfileService } from 'src/modules/pos-profile/service/pos-profile.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { PosProfile } from 'src/modules/pos-profile/entity/pos-profile.entity';
import { PosProfileUpdateHandler } from 'src/modules/pos-profile/jetstream-handler/pos-profile-update.handler';

describe('PosProfileUpdateHandler', () => {
  let handler: PosProfileUpdateHandler;
  let posProfileService: jest.Mocked<PosProfileService>;

  beforeEach(() => {
    posProfileService = { createOrUpdatePosProfile: jest.fn() } as jest.Mocked<
      Partial<PosProfileService>
    >;
    handler = new PosProfileUpdateHandler(posProfileService);
  });

  describe('canHandle', () => {
    it('should return true for POS_PROFILE and UPDATED', () => {
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.POS_PROFILE,
        event: EventTypes.UPDATED,
        data: { name: 'Borne' } as PosProfile,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-POS_PROFILE doctype', () => {
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: { name: 'Borne' } as PosProfile,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-UPDATED event', () => {
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.POS_PROFILE,
        event: EventTypes.DELETED,
        data: { name: 'Borne' } as PosProfile,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call posProfileService.createOrUpdatePosProfile with payload data', async () => {
      const posProfile: PosProfile = {
        name: 'Borne',
        company: 'Le domain',
        currency: 'EUR',
      } as PosProfile;
      const payload: JetStreamPayload<PosProfile> = {
        doctype: Doctypes.POS_PROFILE,
        event: EventTypes.UPDATED,
        data: posProfile,
      };
      await handler.handle(payload);
      expect(posProfileService.createOrUpdatePosProfile).toHaveBeenCalledWith(
        posProfile,
      );
    });
  });
});
