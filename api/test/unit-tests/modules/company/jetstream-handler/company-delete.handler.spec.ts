import { CompanyDeleteHandler } from 'src/modules/company/jetstream-handler/company-delete.handler';
import { CompanyService } from 'src/modules/company/service/company.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { Company } from 'src/modules/company/entity/company.entity';

describe('CompanyDeleteHandler', () => {
  let handler: CompanyDeleteHandler;
  let companyService: jest.Mocked<CompanyService>;

  beforeEach(() => {
    companyService = { deleteCompany: jest.fn() } as jest.Mocked<
      Partial<CompanyService>
    > as jest.Mocked<CompanyService>;
    handler = new CompanyDeleteHandler(companyService);
  });

  describe('canHandle', () => {
    it('should return true for COMPANY and DELETED', () => {
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.COMPANY,
        event: EventTypes.DELETED,
        data: { name: '<PERSON><PERSON>' } as Company,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-COMPANY doctype', () => {
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'Rami' } as Company,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-DELETED event', () => {
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.COMPANY,
        event: EventTypes.UPDATED,
        data: { name: 'Rami' } as Company,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call companyService.deleteCompany with payload data', async () => {
      const company: Company = { name: 'Rami' } as Company;
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.COMPANY,
        event: EventTypes.DELETED,
        data: company,
      };
      await handler.handle(payload);
      expect(companyService.deleteCompany).toHaveBeenCalledWith(company);
    });
  });
});
