import { CompanyUpdateHandler } from 'src/modules/company/jetstream-handler/company-update.handler';
import { CompanyService } from 'src/modules/company/service/company.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { Company } from 'src/modules/company/entity/company.entity';

describe('CompanyUpdateHandler', () => {
  let handler: CompanyUpdateHandler;
  let companyService: jest.Mocked<CompanyService>;

  beforeEach(() => {
    companyService = { createOrUpdateCompany: jest.fn() } as jest.Mocked<
      Partial<CompanyService>
    > as jest.Mocked<CompanyService>;
    handler = new CompanyUpdateHandler(companyService);
  });

  describe('canHandle', () => {
    it('should return true for COMPANY and UPDATED', () => {
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.COMPANY,
        event: EventTypes.UPDATED,
        data: { name: '<PERSON><PERSON>' } as Company,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-COMPANY doctype', () => {
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: { name: 'Rami' } as Company,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-UPDATED event', () => {
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.COMPANY,
        event: EventTypes.DELETED,
        data: { name: 'Rami' } as Company,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call companyService.createOrUpdateCompany with payload data', async () => {
      const company: Company = { name: 'Rami' } as Company;
      const payload: JetStreamPayload<Company> = {
        doctype: Doctypes.COMPANY,
        event: EventTypes.UPDATED,
        data: company,
      };
      await handler.handle(payload);
      expect(companyService.createOrUpdateCompany).toHaveBeenCalledWith(
        company,
      );
    });
  });
});
