import { ItemPriceUpdateHandler } from 'src/modules/pricing/jetstream-handler/item-price-update.handler';
import { PricingService } from 'src/modules/pricing/service/pricing.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { ItemPrice } from 'src/modules/pricing/entity/item-price.entity';

describe('ItemPriceUpdateHandler', () => {
  let handler: ItemPriceUpdateHandler;
  let pricingService: PricingService;

  beforeEach(() => {
    pricingService = { createOrUpdateItemPrice: jest.fn() } as jest.Mocked<
      Partial<PricingService>
    >;
    handler = new ItemPriceUpdateHandler(pricingService);
  });

  describe('canHandle', () => {
    it('should return true for ITEM_PRICE and UPDATED', () => {
      const payload: JetStreamPayload<ItemPrice> = {
        doctype: Doctypes.ITEM_PRICE,
        event: EventTypes.UPDATED,
        data: { name: 'test-item' } as ItemPrice,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for other doctypes or events', () => {
      const payload: JetStreamPayload<ItemPrice> = {
        doctype: Doctypes.ITEM_PRICE,
        event: EventTypes.DELETED,
        data: { name: 'test-item' } as ItemPrice,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call pricingService.createOrUpdateItemPrice with payload data', async () => {
      const payload: JetStreamPayload<ItemPrice> = {
        doctype: Doctypes.ITEM_PRICE,
        event: EventTypes.UPDATED,
        data: { name: 'test' } as ItemPrice,
      };
      await handler.handle(payload);
      expect(pricingService.createOrUpdateItemPrice).toHaveBeenCalledWith(
        payload.data,
      );
    });
  });
});
