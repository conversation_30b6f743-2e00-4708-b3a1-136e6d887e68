import { PricingService } from 'src/modules/pricing/service/pricing.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { PriceList } from 'src/modules/pricing/entity/price-list.entity';
import { PriceListUpdateHandler } from 'src/modules/pricing/jetstream-handler/price-list-update.handler';

describe('PriceListUpdateHandler', () => {
  let handler: PriceListUpdateHandler;
  let pricingService: jest.Mocked<PricingService>;

  beforeEach(() => {
    pricingService = { createOrUpdatePriceList: jest.fn() } as jest.Mocked<
      Partial<PricingService>
    >;
    handler = new PriceListUpdateHandler(pricingService);
  });

  describe('canHandle', () => {
    it('should return true for PRICE_LIST and UPDATED', () => {
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.PRICE_LIST,
        event: EventTypes.UPDATED,
        data: { name: 'list1' } as PriceList,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-PRICE_LIST doctype', () => {
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: { name: 'list1' } as PriceList,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-UPDATED event', () => {
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.PRICE_LIST,
        event: EventTypes.DELETED,
        data: { name: 'list1' } as PriceList,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call pricingService.createOrUpdatePriceList with payload data', async () => {
      const priceList: PriceList = { name: 'list1' } as PriceList;
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.PRICE_LIST,
        event: EventTypes.UPDATED,
        data: priceList,
      };
      await handler.handle(payload);
      expect(pricingService.createOrUpdatePriceList).toHaveBeenCalledWith(
        priceList,
      );
    });
  });
});
