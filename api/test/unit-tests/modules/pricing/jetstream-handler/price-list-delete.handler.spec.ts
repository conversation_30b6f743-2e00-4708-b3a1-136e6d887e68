import { PricingService } from 'src/modules/pricing/service/pricing.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { PriceList } from 'src/modules/pricing/entity/price-list.entity';
import { PriceListDeleteHandler } from 'src/modules/pricing/jetstream-handler/price-list-delete.handler';

describe('PriceListDeleteHandler', () => {
  let handler: PriceListDeleteHandler;
  let pricingService: jest.Mocked<PricingService>;

  beforeEach(() => {
    pricingService = { deletePriceList: jest.fn() } as jest.Mocked<
      Partial<PricingService>
    >;
    handler = new PriceListDeleteHandler(pricingService);
  });

  describe('canHandle', () => {
    it('should return true for PRICE_LIST and DELETED', () => {
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.PRICE_LIST,
        event: EventTypes.DELETED,
        data: { name: 'list1' } as PriceList,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-PRICE_LIST doctype', () => {
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'list1' } as PriceList,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-DELETED event', () => {
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.PRICE_LIST,
        event: EventTypes.UPDATED,
        data: { name: 'list1' } as PriceList,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call pricingService.deletePriceList with payload data', async () => {
      const priceList: PriceList = { name: 'list1' } as PriceList;
      const payload: JetStreamPayload<PriceList> = {
        doctype: Doctypes.PRICE_LIST,
        event: EventTypes.DELETED,
        data: priceList,
      };
      await handler.handle(payload);
      expect(pricingService.deletePriceList).toHaveBeenCalledWith(priceList);
    });
  });
});
