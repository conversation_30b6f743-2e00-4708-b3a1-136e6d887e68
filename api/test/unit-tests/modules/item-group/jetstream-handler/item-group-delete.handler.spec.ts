import { ItemGroupDeleteHandler } from 'src/modules/item-group/jetstream-handler/item-group-delete.handler';
import { ItemGroupService } from 'src/modules/item-group/service/item-group.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { ItemGroup } from 'src/modules/item-group/entity/item-group.entity';

describe('ItemGroupDeleteHandler', () => {
  let handler: ItemGroupDeleteHandler;
  let itemGroupService: jest.Mocked<ItemGroupService>;

  beforeEach(() => {
    itemGroupService = { deleteItemGroup: jest.fn() } as jest.Mocked<
      Partial<ItemGroupService>
    >;
    handler = new ItemGroupDeleteHandler(itemGroupService);
  });

  describe('canHandle', () => {
    it('should return true for ITEM_GROUP and DELETED', () => {
      const payload: JetStreamPayload<ItemGroup> = {
        doctype: Doctypes.ITEM_GROUP,
        event: EventTypes.DELETED,
        data: { name: 'group1' } as ItemGroup,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-ITEM_GROUP doctype', () => {
      const payload: JetStreamPayload<ItemGroup> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'group1' } as ItemGroup,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-DELETED event', () => {
      const payload: JetStreamPayload<ItemGroup> = {
        doctype: Doctypes.ITEM_GROUP,
        event: EventTypes.UPDATED,
        data: { name: 'group1' } as ItemGroup,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call itemGroupService.deleteItemGroup with payload data name', async () => {
      const itemGroup: ItemGroup = { name: 'group1' } as ItemGroup;
      const payload: JetStreamPayload<ItemGroup> = {
        doctype: Doctypes.ITEM_GROUP,
        event: EventTypes.DELETED,
        data: itemGroup,
      };
      await handler.handle(payload);
      expect(itemGroupService.deleteItemGroup).toHaveBeenCalledWith(
        itemGroup.name,
      );
    });
  });
});
