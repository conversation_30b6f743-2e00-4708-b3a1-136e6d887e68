import { ModeOfPaymentUpdateHandler } from 'src/modules/mode-of-payment/jetstream-handler/mode-of-payment-update.handler';
import { ModeOfPaymentService } from 'src/modules/mode-of-payment/service/mode-of-payment.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { ModeOfPayment } from 'src/modules/mode-of-payment/entity/mode-of-payment.entity';

describe('ModeOfPaymentUpdateHandler', () => {
  let handler: ModeOfPaymentUpdateHandler;
  let modeOfPaymentService: jest.Mocked<ModeOfPaymentService>;

  beforeEach(() => {
    modeOfPaymentService = {
      createOrUpdateModeOfPayment: jest.fn(),
    } as jest.Mocked<Partial<ModeOfPaymentService>>;
    handler = new ModeOfPaymentUpdateHandler(modeOfPaymentService);
  });

  describe('canHandle', () => {
    it('should return true for MODE_OF_PAYMENT and UPDATED', () => {
      const payload: JetStreamPayload<ModeOfPayment> = {
        doctype: Doctypes.MODE_OF_PAYMENT,
        event: EventTypes.UPDATED,
        data: { name: 'cash' } as ModeOfPayment,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-MODE_OF_PAYMENT doctype', () => {
      const payload: JetStreamPayload<ModeOfPayment> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.UPDATED,
        data: { name: 'cash' } as ModeOfPayment,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-UPDATED event', () => {
      const payload: JetStreamPayload<ModeOfPayment> = {
        doctype: Doctypes.MODE_OF_PAYMENT,
        event: EventTypes.DELETED,
        data: { name: 'cash' } as ModeOfPayment,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call modeOfPaymentService.createOrUpdateModeOfPayment with payload data', async () => {
      const modeOfPayment: ModeOfPayment = {
        name: 'bil karta',
      } as ModeOfPayment;
      const payload: JetStreamPayload<ModeOfPayment> = {
        doctype: Doctypes.MODE_OF_PAYMENT,
        event: EventTypes.UPDATED,
        data: modeOfPayment,
      };
      await handler.handle(payload);
      expect(
        modeOfPaymentService.createOrUpdateModeOfPayment,
      ).toHaveBeenCalledWith(modeOfPayment);
    });
  });
});
