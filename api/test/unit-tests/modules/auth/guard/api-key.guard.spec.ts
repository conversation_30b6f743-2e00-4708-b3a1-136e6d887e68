import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ApiKeyGuard } from '../../../../../src/modules/auth/guard/api-key.guard';

describe('ApiKeyGuard', () => {
  let guard: ApiKeyGuard;
  let mockExecutionContext: ExecutionContext;
  let mockRequest: any;

  beforeEach(() => {
    guard = new ApiKeyGuard();

    mockRequest = {
      headers: {},
    };

    mockExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;
  });

  afterEach(() => {
    delete process.env.API_KEY;
  });

  describe('canActivate', () => {
    it('should throw UnauthorizedException when authorization header is missing', () => {
      // Arrange
      mockRequest.headers = {};

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        new UnauthorizedException('Authorization header is required'),
      );
    });

    it('should throw UnauthorizedException when authorization header does not start with Bear<PERSON>', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Basic some-token',
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        new UnauthorizedException(
          'Invalid authorization format. Use Bearer token',
        ),
      );
    });

    it('should throw UnauthorizedException when Bearer token is empty', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer ',
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        new UnauthorizedException('API key is required'),
      );
    });

    it('should throw UnauthorizedException when API_KEY environment variable is not set', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer valid-api-key',
      };
      delete process.env.API_KEY;

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        new UnauthorizedException('API key not configured'),
      );
    });

    it('should throw UnauthorizedException when API key does not match', () => {
      // Arrange
      process.env.API_KEY = 'correct-api-key';
      mockRequest.headers = {
        authorization: 'Bearer wrong-api-key',
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        new UnauthorizedException('Invalid API key'),
      );
    });

    it('should return true when API key is valid', () => {
      // Arrange
      process.env.API_KEY = 'valid-api-key';
      mockRequest.headers = {
        authorization: 'Bearer valid-api-key',
      };

      // Act
      const result = guard.canActivate(mockExecutionContext);

      // Assert
      expect(result).toBe(true);
    });

    it('should work with different header casing', () => {
      // Arrange
      process.env.API_KEY = 'valid-api-key';
      mockRequest.headers = {
        authorization: 'Bearer valid-api-key', // lowercase
      };

      // Act
      const result = guard.canActivate(mockExecutionContext);

      // Assert
      expect(result).toBe(true);
    });
  });
});
