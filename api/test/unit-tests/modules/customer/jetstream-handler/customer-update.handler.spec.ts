import { CustomerUpdateHandler } from 'src/modules/customer/jetstream-handler/customer-update.handler';
import { CustomerService } from 'src/modules/customer/service/customer.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { Customer } from 'src/modules/customer/entity/customer.entity';

describe('CustomerUpdateHandler', () => {
  let handler: CustomerUpdateHandler;
  let customerService: jest.Mocked<CustomerService>;

  beforeEach(() => {
    customerService = { createOrUpdateCustomer: jest.fn() } as jest.Mocked<
      Partial<CustomerService>
    >;
    handler = new CustomerUpdateHandler(customerService);
  });

  describe('canHandle', () => {
    it('should return true for CUSTOMER and UPDATED', () => {
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.UPDATED,
        data: { name: 'Walk in customer' } as Customer,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-CUSTOMER doctype', () => {
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.UPDATED,
        data: { name: 'Walk in customer' } as Customer,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-UPDATED event', () => {
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.DELETED,
        data: { name: 'Walk in customer' } as Customer,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call customerService.createOrUpdateCustomer with payload data', async () => {
      const customer: Customer = { name: 'Walk in customer' } as Customer;
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.UPDATED,
        data: customer,
      };
      await handler.handle(payload);
      expect(customerService.createOrUpdateCustomer).toHaveBeenCalledWith(
        customer,
      );
    });
  });
});
