import { CustomerDeleteHandler } from 'src/modules/customer/jetstream-handler/customer-delete.handler';
import { CustomerService } from 'src/modules/customer/service/customer.service';
import { Doctypes } from 'src/modules/jetstream/enum/doctypes';
import { EventTypes } from 'src/modules/jetstream/enum/event-types';
import { JetStreamPayload } from 'src/modules/jetstream/interface/jetstream-payload';
import { Customer } from 'src/modules/customer/entity/customer.entity';

describe('CustomerDeleteHandler', () => {
  let handler: CustomerDeleteHandler;
  let customerService: jest.Mocked<CustomerService>;

  beforeEach(() => {
    customerService = { deleteCustomer: jest.fn() } as jest.Mocked<
      Partial<CustomerService>
    >;
    handler = new CustomerDeleteHandler(customerService);
  });

  describe('canHandle', () => {
    it('should return true for CUSTOMER and DELETED', () => {
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.DELETED,
        data: { name: 'Walk in customer' } as Customer,
      };
      expect(handler.canHandle(payload)).toBe(true);
    });

    it('should return false for non-CUSTOMER doctype', () => {
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.ITEM,
        event: EventTypes.DELETED,
        data: { name: 'Walk in customer' } as Customer,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });

    it('should return false for non-DELETED event', () => {
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.UPDATED,
        data: { name: 'Walk in customer' } as Customer,
      };
      expect(handler.canHandle(payload)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should call customerService.deleteCustomer with payload data', async () => {
      const customer: Customer = { name: 'Walk in customer' } as Customer;
      const payload: JetStreamPayload<Customer> = {
        doctype: Doctypes.CUSTOMER,
        event: EventTypes.DELETED,
        data: customer,
      };
      await handler.handle(payload);
      expect(customerService.deleteCustomer).toHaveBeenCalledWith(customer);
    });
  });
});
