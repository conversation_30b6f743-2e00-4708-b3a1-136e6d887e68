import { PostgreSqlContainer } from '@testcontainers/postgresql';
import { DataSource } from 'typeorm';
import * as path from 'path';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { Pool } from 'pg';
import * as fs from 'fs';

const DB_INFO_PATH = path.join(__dirname, '.test-db.json');

interface DbInfo {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
}

export class TestDatabase {
  private static container: any;
  private static dataSource: DataSource;

  static async start() {
    try {
      // Start PostgreSQL container
      this.container = await new PostgreSqlContainer('postgres:16-alpine')
        .withDatabase('test_db')
        .withUsername('test_user')
        .withPassword('test_password')
        .start();

      // Write connection info to file for test processes
      const dbInfo = {
        host: this.container.getHost(),
        port: this.container.getPort(),
        username: this.container.getUsername(),
        password: this.container.getPassword(),
        database: this.container.getDatabase(),
      };
      fs.writeFileSync(DB_INFO_PATH, JSON.stringify(dbInfo));

      // Create TypeORM connection (for global setup only)
      const entitiesPath = path.join(
        __dirname,
        '..',
        'src',
        '**',
        '*.entity.{ts,js}',
      );

      this.dataSource = new DataSource({
        type: 'postgres',
        ...dbInfo,
        entities: [entitiesPath],
        synchronize: true,
        dropSchema: true,
        logging: false,
        namingStrategy: new SnakeNamingStrategy(),
        extra: {
          Pool: Pool,
        },
      });

      await this.dataSource.initialize();
      return this.dataSource;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      // Ensure cleanup on failure
      await this.stop();
      throw error;
    }
  }

  static async stop() {
    try {
      if (this.dataSource?.isInitialized) {
        await this.dataSource.destroy();
      }
      if (this.container) {
        await this.container.stop();
      }
      // Clean up the db info file
      if (fs.existsSync(DB_INFO_PATH)) {
        fs.unlinkSync(DB_INFO_PATH);
      }
    } catch (error) {
      console.error('Error during database cleanup:', error);
    }
  }

  static getDataSource() {
    return this.dataSource;
  }

  static getDbInfo(): DbInfo {
    if (!fs.existsSync(DB_INFO_PATH)) {
      throw new Error('Test DB info file not found. Did you run global setup?');
    }
    return JSON.parse(fs.readFileSync(DB_INFO_PATH, 'utf-8'));
  }
}
