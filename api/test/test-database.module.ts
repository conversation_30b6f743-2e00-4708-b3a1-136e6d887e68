import { Module, Global } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { TestDatabase } from './test-database';
import { ItemGroup } from 'src/modules/item-group/entity/item-group.entity'; // Add other entities as needed
import { Item } from 'src/modules/item/entity/item.entity';
import { PriceList } from 'src/modules/pricing/entity/price-list.entity';
import { ItemPrice } from 'src/modules/pricing/entity/item-price.entity';
import { PosProfile } from 'src/modules/pos-profile/entity/pos-profile.entity';
import { Customer } from 'src/modules/customer/entity/customer.entity';
import { ModeOfPayment } from 'src/modules/mode-of-payment/entity/mode-of-payment.entity';
import { Company } from 'src/modules/company/entity/company.entity';

@Global()
@Module({
  providers: [
    {
      provide: DataSource,
      useFactory: async () => {
        const dbInfo = TestDatabase.getDbInfo();
        const dataSource = new DataSource({
          type: 'postgres',
          ...dbInfo,
          entities: [
            ItemGroup,
            Item,
            PriceList,
            ItemPrice,
            PosProfile,
            Customer,
            ModeOfPayment,
            Company,
          ], // Add all your entities here or use a glob
          synchronize: false,
          dropSchema: false,
          logging: false,
          namingStrategy: new SnakeNamingStrategy(),
        });
        await dataSource.initialize();
        return dataSource;
      },
    },
  ],
  exports: [DataSource],
})
export class TestDatabaseModule {}
