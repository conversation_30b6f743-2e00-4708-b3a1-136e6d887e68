import { Module } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { ItemGroupModule } from '../src/modules/item-group/item-group.module';
import { TestDatabase } from './test-database';
import { ItemGroup } from '../src/modules/item-group/entity/item-group.entity';
import { Item } from '../src/modules/item/entity/item.entity';
import { PriceList } from '../src/modules/pricing/entity/price-list.entity';
import { ItemPrice } from '../src/modules/pricing/entity/item-price.entity';
import { PosProfile } from '../src/modules/pos-profile/entity/pos-profile.entity';
import { Customer } from '../src/modules/customer/entity/customer.entity';
import { ModeOfPayment } from '../src/modules/mode-of-payment/entity/mode-of-payment.entity';
import { Company } from '../src/modules/company/entity/company.entity';
import { ItemModule } from '../src/modules/item/item.module';
import { PricingModule } from '../src/modules/pricing/pricing.module';
import { PosProfileModule } from '../src/modules/pos-profile/pos-profile.module';
import { CustomerModule } from '../src/modules/customer/customer.module';
import { ModeOfPaymentModule } from '../src/modules/mode-of-payment/mode-of-payment.module';
import { CompanyModule } from '../src/modules/company/company.module';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: (): TypeOrmModuleOptions => {
        const dbInfo = TestDatabase.getDbInfo();
        return {
          type: 'postgres',
          ...dbInfo,
          entities: [
            ItemGroup,
            Item,
            PriceList,
            ItemPrice,
            PosProfile,
            Customer,
            ModeOfPayment,
            Company,
          ],
          namingStrategy: new SnakeNamingStrategy(),
          synchronize: false,
          dropSchema: false,
          logging: false,
        };
      },
    }),
    ItemGroupModule,
    ItemModule,
    PricingModule,
    PosProfileModule,
    CustomerModule,
    ModeOfPaymentModule,
    CompanyModule,
  ],
})
export class TestAppModule {}
