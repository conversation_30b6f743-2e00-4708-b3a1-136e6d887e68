import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { PosProfile } from 'src/modules/pos-profile/entity/pos-profile.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for PosProfileController using a real PostgreSQL database via Testcontainers.
 */
describe('PosProfileController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testPosProfiles: PosProfile[] = [
    {
      name: 'Borne',
      company: 'Le domain',
      currency: 'EUR',
      country: 'France',
      selling_price_list: 'Le domain selling',
      warehouse: 'Le domaine default warehouse - Ld',
      applicable_for_users: [
        {
          user: null,
          default: 0,
          parent: 'Borne',
          parentfield: 'applicable_for_users',
          parenttype: 'POS Profile',
        },
      ],
      customer_groups: [
        {
          customer_group: 'Individual',
          parent: 'Borne',
          parentfield: 'customer_groups',
          parenttype: 'POS Profile',
        },
      ],
      payments: [
        {
          mode_of_payment: 'Cash',
          default: 1,
          allow_in_returns: 0,
          parent: 'Borne',
          parentfield: 'payments',
          parenttype: 'POS Profile',
        },
      ],
      item_groups: [],
    },
    {
      name: 'Restaurant',
      company: 'Le domain',
      currency: 'USD',
      country: 'USA',
      selling_price_list: 'Restaurant selling',
      warehouse: 'Restaurant warehouse - R',
      applicable_for_users: [],
      customer_groups: [
        {
          customer_group: 'Commercial',
          parent: 'Restaurant',
          parentfield: 'customer_groups',
          parenttype: 'POS Profile',
        },
      ],
      payments: [
        {
          mode_of_payment: 'Credit Card',
          default: 1,
          allow_in_returns: 1,
          parent: 'Restaurant',
          parentfield: 'payments',
          parenttype: 'POS Profile',
        },
      ],
      item_groups: [],
    },
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    for (const posProfile of testPosProfiles) {
      await dataSource.getRepository(PosProfile).save(posProfile);
    }
  });

  afterAll(async () => {
    for (const posProfile of testPosProfiles) {
      await dataSource
        .getRepository(PosProfile)
        .delete({ name: posProfile.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /pos-profiles', () => {
    it('should return all POS profiles', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pos-profiles')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as PosProfile[]).length).toBe(
        testPosProfiles.length,
      );
      expect(response.body).toEqual(
        expect.arrayContaining(
          testPosProfiles.map((pp): object => {
            return expect.objectContaining({
              name: pp.name,
              company: pp.company,
              currency: pp.currency,
              country: pp.country,
              selling_price_list: pp.selling_price_list,
              warehouse: pp.warehouse,
            }) as object;
          }),
        ),
      );
    });
  });

  describe('GET /pos-profiles/:name', () => {
    it('should return POS profile by name', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pos-profiles/Borne')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(response.body).toEqual(
        expect.objectContaining({
          name: 'Borne',
          company: 'Le domain',
          currency: 'EUR',
          country: 'France',
          selling_price_list: 'Le domain selling',
          warehouse: 'Le domaine default warehouse - Ld',
        }),
      );
      expect((response.body as PosProfile).applicable_for_users).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            user: null,
            default: 0,
            parent: 'Borne',
          }),
        ]),
      );
      expect((response.body as PosProfile).customer_groups).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            customer_group: 'Individual',
            parent: 'Borne',
          }),
        ]),
      );
      expect((response.body as PosProfile).payments).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            mode_of_payment: 'Cash',
            default: 1,
            allow_in_returns: 0,
          }),
        ]),
      );
    });

    it('should return empty object when POS profile is not found', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pos-profiles/NONEXISTENT')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(response.body).toEqual({});
    });
  });
});
