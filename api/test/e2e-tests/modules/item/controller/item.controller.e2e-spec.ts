import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Item } from 'src/modules/item/entity/item.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for ItemController using a real PostgreSQL database via Testcontainers.
 */
describe('ItemController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testItems: Item[] = [
    {
      name: 'TestItem1',
      description: 'A test item 1',
      item_group: 'GroupA',
    },
    {
      name: 'TestItem2',
      description: 'A test item 2',
      item_group: 'GroupB',
    },
    {
      name: 'TestItem3',
      description: 'A test item 3',
      item_group: 'GroupA',
    },
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);
    for (const item of testItems) {
      await dataSource.getRepository(Item).save(item);
    }
  });

  afterAll(async () => {
    for (const item of testItems) {
      await dataSource.getRepository(Item).delete({ name: item.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /items', () => {
    it('should return all items', async () => {
      const response: request.Response = await request(httpServer)
        .get('/items')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as Item[]).length).toBe(testItems.length);
      expect(response.body).toEqual(
        expect.arrayContaining(
          testItems.map((i): object => {
            return expect.objectContaining({
              name: i.name,
              description: i.description,
              item_group: i.item_group,
            }) as object;
          }),
        ),
      );
    });
  });

  describe('GET /items?item_group=...', () => {
    it('should return items filtered by item_group', async () => {
      const response: request.Response = await request(httpServer)
        .get('/items?item_group=GroupA')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      const expected = testItems.filter((i) => i.item_group === 'GroupA');
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as Item[]).length).toBe(expected.length);
      expect(response.body).toEqual(
        expect.arrayContaining(
          expected.map((i): object => {
            return expect.objectContaining({
              name: i.name,
              description: i.description,
              item_group: i.item_group,
            }) as object;
          }),
        ),
      );
    });

    it('should return empty array if no items match the filter', async () => {
      const response: request.Response = await request(httpServer)
        .get('/items?item_group=NonExistentGroup')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as Item[]).length).toBe(0);
    });
  });
});
