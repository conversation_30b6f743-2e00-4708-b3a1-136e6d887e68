import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { TestAppModule } from '../../../test-app.module';
import { HealthModule } from '../../../../src/modules/health/health.module';
import { AuthModule } from '../../../../src/modules/auth/auth.module';

describe('API Foundation - Versioning & Authentication (e2e)', () => {
  let app: INestApplication;
  let httpServer: any;

  beforeAll(async () => {
    // Set a test API key for these tests
    process.env.API_KEY = 'test-api-key-123';

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule, HealthModule, AuthModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // Apply the same global prefix as in main.ts
    app.setGlobalPrefix('api/v1');

    await app.init();
    httpServer = app.getHttpServer();
  });

  afterAll(async () => {
    delete process.env.API_KEY;
    await app.close();
  });

  describe('API Versioning', () => {
    it('should return 404 for requests without /api/v1/ prefix', async () => {
      const response = await request(httpServer).get('/health');

      expect(response.status).toBe(404);
    });

    it('should accept requests with /api/v1/ prefix for health endpoint', async () => {
      const response = await request(httpServer).get('/api/v1/health');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ status: 'ok' });
    });
  });

  describe('Health Endpoint - Should be public', () => {
    it('should allow access to health endpoint without authentication', async () => {
      const response = await request(httpServer).get('/api/v1/health');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ status: 'ok' });
    });
  });

  describe('API Key Authentication for Protected Endpoints', () => {
    const protectedEndpoints = [
      '/api/v1/item-groups',
      '/api/v1/items',
      '/api/v1/pricing/price-lists',
      '/api/v1/pos-profiles',
    ];

    protectedEndpoints.forEach((endpoint) => {
      describe(`${endpoint} endpoint`, () => {
        it('should return 401 for requests without Authorization header', async () => {
          const response = await request(httpServer).get(endpoint);

          expect(response.status).toBe(401);
          expect(response.body.message).toBe(
            'Authorization header is required',
          );
        });

        it('should return 401 for requests with invalid authorization format', async () => {
          const response = await request(httpServer)
            .get(endpoint)
            .set('Authorization', 'Basic invalid-format');

          expect(response.status).toBe(401);
          expect(response.body.message).toBe(
            'Invalid authorization format. Use Bearer token',
          );
        });

        it('should return 401 for requests with empty Bearer token', async () => {
          const response = await request(httpServer)
            .get(endpoint)
            .set('Authorization', 'Bearer');

          expect(response.status).toBe(401);
          expect(response.body.message).toBe(
            'Invalid authorization format. Use Bearer token',
          );
        });

        it('should return 401 for requests with invalid API key', async () => {
          const response = await request(httpServer)
            .get(endpoint)
            .set('Authorization', 'Bearer invalid-api-key');

          expect(response.status).toBe(401);
          expect(response.body.message).toBe('Invalid API key');
        });

        it('should allow access with valid API key', async () => {
          const response = await request(httpServer)
            .get(endpoint)
            .set('Authorization', 'Bearer test-api-key-123');

          // Should not be 401 (may be 200, 404, or other depending on endpoint and data)
          expect(response.status).not.toBe(401);
        });
      });
    });
  });

  describe('API Key Configuration', () => {
    it('should return 401 when API_KEY environment variable is not configured', async () => {
      // Temporarily remove API_KEY
      const originalApiKey = process.env.API_KEY;
      delete process.env.API_KEY;

      const response = await request(httpServer)
        .get('/api/v1/item-groups')
        .set('Authorization', 'Bearer any-key');

      expect(response.status).toBe(401);
      expect(response.body.message).toBe('API key not configured');

      // Restore API_KEY
      process.env.API_KEY = originalApiKey;
    });
  });
});
