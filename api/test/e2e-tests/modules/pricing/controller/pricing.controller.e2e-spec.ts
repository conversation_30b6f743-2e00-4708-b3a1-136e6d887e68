import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { PriceList } from 'src/modules/pricing/entity/price-list.entity';
import { ItemPrice } from 'src/modules/pricing/entity/item-price.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for PricingController using a real PostgreSQL database via Testcontainers.
 */
describe('PricingController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testPriceLists: PriceList[] = [
    {
      name: 'Standard',
      currency: 'USD',
      enabled: 1,
    },
    {
      name: 'Premium',
      currency: 'USD',
      enabled: 1,
    },
  ];

  const testItemPrices: ItemPrice[] = [
    {
      name: 'ITEM001-Standard',
      item_code: 'ITEM001',
      price_list: 'Standard',
      price_list_rate: 10.99,
      currency: 'USD',
    },
    {
      name: 'ITEM001-Premium',
      item_code: 'ITEM001',
      price_list: 'Premium',
      price_list_rate: 12.99,
      currency: 'USD',
    },
    {
      name: 'ITEM002-Standard',
      item_code: 'ITEM002',
      price_list: 'Standard',
      price_list_rate: 15.99,
      currency: 'USD',
    },
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    for (const priceList of testPriceLists) {
      await dataSource.getRepository(PriceList).save(priceList);
    }
    for (const itemPrice of testItemPrices) {
      await dataSource.getRepository(ItemPrice).save(itemPrice);
    }
  });

  afterAll(async () => {
    for (const itemPrice of testItemPrices) {
      await dataSource
        .getRepository(ItemPrice)
        .delete({ name: itemPrice.name });
    }
    for (const priceList of testPriceLists) {
      await dataSource
        .getRepository(PriceList)
        .delete({ name: priceList.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /pricing/price-lists', () => {
    it('should return all price lists', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/price-lists')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as PriceList[]).length).toBe(testPriceLists.length);
      expect(response.body).toEqual(
        expect.arrayContaining(
          testPriceLists.map((pl): object => {
            return expect.objectContaining({
              name: pl.name,
              currency: pl.currency,
              enabled: pl.enabled,
            }) as object;
          }),
        ),
      );
    });
  });

  describe('GET /pricing/item-prices', () => {
    it('should return all item prices', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/item-prices')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemPrice[]).length).toBe(testItemPrices.length);
      expect(response.body).toEqual(
        expect.arrayContaining(
          testItemPrices.map((ip): object => {
            return expect.objectContaining({
              name: ip.name,
              item_code: ip.item_code,
              price_list: ip.price_list,
              price_list_rate: ip.price_list_rate,
              currency: ip.currency,
            }) as object;
          }),
        ),
      );
    });

    it('should return item prices filtered by price_list', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/item-prices?price_list=Standard')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      const expected = testItemPrices.filter(
        (ip) => ip.price_list === 'Standard',
      );
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemPrice[]).length).toBe(expected.length);
      expect(response.body).toEqual(
        expect.arrayContaining(
          expected.map((ip): object => {
            return expect.objectContaining({
              name: ip.name,
              item_code: ip.item_code,
              price_list: ip.price_list,
              price_list_rate: ip.price_list_rate,
              currency: ip.currency,
            }) as object;
          }),
        ),
      );
    });

    it('should return empty array if no items match the price_list filter', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/item-prices?price_list=NonExistentPriceList')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemPrice[]).length).toBe(0);
    });
  });

  describe('GET /pricing/item-prices/:itemCode', () => {
    it('should return item price for specific item code', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/item-prices/ITEM001')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(response.body).toEqual(
        expect.objectContaining({
          item_code: 'ITEM001',
          price_list_rate: expect.any(Number) as number,
          currency: expect.any(String) as string,
        }),
      );
    });

    it('should return item price for specific item code and price list', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/item-prices/ITEM001?price_list=Premium')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(response.body).toEqual(
        expect.objectContaining({
          item_code: 'ITEM001',
          price_list: 'Premium',
          price_list_rate: 12.99,
          currency: 'USD',
        }),
      );
    });

    it('should return null when item price is not found', async () => {
      const response: request.Response = await request(httpServer)
        .get('/pricing/item-prices/NONEXISTENT')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(response.body).toEqual({});
    });
  });
});
