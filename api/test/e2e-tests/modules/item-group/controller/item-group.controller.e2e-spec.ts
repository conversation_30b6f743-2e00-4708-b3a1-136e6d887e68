import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { ItemGroup } from 'src/modules/item-group/entity/item-group.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for ItemGroupController using a real PostgreSQL database via Testcontainers.
 */
describe('ItemGroupController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testItemGroups: ItemGroup[] = [
    {
      name: 'TestGroup',
      description: 'A test item group',
    },
    {
      name: 'TestGroup2',
      description: 'A test item group 2',
    },
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);
    // Clear the item_group table before seeding
    await dataSource.getRepository(ItemGroup).clear();
    for (const itemGroup of testItemGroups) {
      await dataSource.getRepository(ItemGroup).save(itemGroup);
    }
  });

  afterAll(async () => {
    for (const itemGroup of testItemGroups) {
      await dataSource
        .getRepository(ItemGroup)
        .delete({ name: itemGroup.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /item-groups', () => {
    it('should return all item groups', async () => {
      const response: request.Response = await request(httpServer)
        .get('/item-groups')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemGroup[]).length).toBe(testItemGroups.length);
      // Only check name and description for equality
      expect(response.body).toEqual(
        expect.arrayContaining(
          testItemGroups.map((g): object => {
            return expect.objectContaining({
              name: g.name,
              description: g.description,
            }) as object;
          }),
        ),
      );
    });
  });

  describe('GET /item-groups/:name', () => {
    it('should return an item group by name', async () => {
      const response: request.Response = await request(httpServer)
        .get(`/item-groups/${testItemGroups[0].name}`)
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(response.body).toEqual(
        expect.objectContaining({
          name: testItemGroups[0].name,
          description: testItemGroups[0].description,
        }),
      );
    });

    it('should return 404 if item group not found', async () => {
      await request(httpServer)
        .get(`/item-groups/non-existent-group`)
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(404);
    });
  });

  describe('GET /item-groups/:name/children', () => {
    it('should return children for a group with no children (empty array)', async () => {
      const response: request.Response = await request(httpServer)
        .get(`/item-groups/${testItemGroups[0].name}/children`)
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemGroup[]).length).toBe(0);
    });

    // Note: The current service/controller returns an empty array if the parent group does not exist.
    // If you want a 404 here, you need to update the service/controller logic.
    it('should return empty array for non-existent parent group', async () => {
      const response: request.Response = await request(httpServer)
        .get('/item-groups/non-existent-group/children')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemGroup[]).length).toBe(0);
    });

    it('should return correct children for a group with children', async () => {
      // Add a child group to testItemGroups[0]
      const childGroup = {
        name: 'ChildGroup',
        description: 'A child item group',
        parent_item_group: testItemGroups[0].name,
      } as ItemGroup;
      await dataSource.getRepository(ItemGroup).save(childGroup);

      const response: request.Response = await request(httpServer)
        .get(`/item-groups/${testItemGroups[0].name}/children`)
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ItemGroup[]).length).toBe(1);
      expect((response.body as ItemGroup[])[0].name).toBe(childGroup.name);
      expect((response.body as ItemGroup[])[0].parent_item_group).toBe(
        childGroup.parent_item_group,
      );

      // Clean up
      await dataSource
        .getRepository(ItemGroup)
        .delete({ name: childGroup.name });
    });
  });
});
