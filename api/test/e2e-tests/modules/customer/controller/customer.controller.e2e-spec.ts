import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Customer } from 'src/modules/customer/entity/customer.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for CustomerController using a real PostgreSQL database via Testcontainers.
 */
describe('CustomerController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testCustomers: Customer[] = [
    {
      name: 'Walk in customer',
      customer_name: 'Walk in customer',
      customer_type: 'Individual',
      language: 'en',
      disabled: 0,
    } as Customer,
    {
      name: 'Test Customer 2',
      customer_name: 'Test Customer 2',
      customer_type: 'Company',
      language: 'en',
      disabled: 0,
    } as Customer,
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    // Clear the customer table before seeding
    await dataSource.getRepository(Customer).clear();
    for (const customer of testCustomers) {
      await dataSource.getRepository(Customer).save(customer);
    }
  });

  afterAll(async () => {
    for (const customer of testCustomers) {
      await dataSource.getRepository(Customer).delete({ name: customer.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /customers', () => {
    it('should return all customers', async () => {
      const response: request.Response = await request(httpServer)
        .get('/customers')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as Customer[]).length).toBe(testCustomers.length);
      // Check for key fields
      expect(response.body).toEqual(
        expect.arrayContaining(
          testCustomers.map((customer): object => {
            return expect.objectContaining({
              name: customer.name,
              customer_name: customer.customer_name,
              customer_type: customer.customer_type,
            }) as object;
          }),
        ),
      );
    });

    it('should require authentication', async () => {
      await request(httpServer).get('/customers').expect(401);
    });

    it('should reject invalid API key', async () => {
      await request(httpServer)
        .get('/customers')
        .set('Authorization', 'Bearer invalid-key')
        .expect(401);
    });
  });
});
