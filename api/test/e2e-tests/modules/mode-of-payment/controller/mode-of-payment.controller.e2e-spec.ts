import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { ModeOfPayment } from 'src/modules/mode-of-payment/entity/mode-of-payment.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for ModeOfPaymentController using a real PostgreSQL database via Testcontainers.
 */
describe('ModeOfPaymentController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testModeOfPayments: ModeOfPayment[] = [
    {
      name: 'bil karta',
      mode_of_payment: 'bil karta',
      type: 'Cash',
      enabled: 1,
    } as ModeOfPayment,
    {
      name: 'Credit Card',
      mode_of_payment: 'Credit Card',
      type: 'Bank',
      enabled: 1,
    } as ModeOfPayment,
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    // Clear the mode_of_payment table before seeding
    await dataSource.getRepository(ModeOfPayment).clear();
    for (const modeOfPayment of testModeOfPayments) {
      await dataSource.getRepository(ModeOfPayment).save(modeOfPayment);
    }
  });

  afterAll(async () => {
    for (const modeOfPayment of testModeOfPayments) {
      await dataSource
        .getRepository(ModeOfPayment)
        .delete({ name: modeOfPayment.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /modes-of-payment', () => {
    it('should return all modes of payment', async () => {
      const response: request.Response = await request(httpServer)
        .get('/modes-of-payment')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as ModeOfPayment[]).length).toBe(
        testModeOfPayments.length,
      );
      // Check for key fields
      expect(response.body).toEqual(
        expect.arrayContaining(
          testModeOfPayments.map((modeOfPayment): object => {
            return expect.objectContaining({
              name: modeOfPayment.name,
              mode_of_payment: modeOfPayment.mode_of_payment,
              type: modeOfPayment.type,
            }) as object;
          }),
        ),
      );
    });

    it('should require authentication', async () => {
      await request(httpServer).get('/modes-of-payment').expect(401);
    });

    it('should reject invalid API key', async () => {
      await request(httpServer)
        .get('/modes-of-payment')
        .set('Authorization', 'Bearer invalid-key')
        .expect(401);
    });
  });
});
