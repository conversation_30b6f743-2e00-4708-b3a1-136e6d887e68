import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Company } from 'src/modules/company/entity/company.entity';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for CompanyController using a real PostgreSQL database via Testcontainers.
 */
describe('CompanyController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testCompanies: Partial<Company>[] = [
    {
      name: '<PERSON>i',
      doctype: 'Company',
      company_name: '<PERSON><PERSON>',
    },
    {
      name: 'saba technology oman',
      doctype: 'Company',
      company_name: 'saba technology oman',
    },
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    // Clear the company table before seeding
    await dataSource.getRepository(Company).clear();
    for (const company of testCompanies) {
      await dataSource.getRepository(Company).save(company);
    }
  });

  afterAll(async () => {
    for (const company of testCompanies) {
      await dataSource.getRepository(Company).delete({ name: company.name });
    }
    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  describe('GET /companies', () => {
    it('should return all companies', async () => {
      const response: request.Response = await request(httpServer)
        .get('/companies')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect((response.body as Company[]).length).toBe(testCompanies.length);
      // Check for key fields
      expect(response.body).toEqual(
        expect.arrayContaining(
          testCompanies.map((company): object => {
            return expect.objectContaining({
              name: company.name,
              doctype: company.doctype,
              company_name: company.company_name,
            }) as object;
          }),
        ),
      );
    });

    it('should require authentication', async () => {
      await request(httpServer).get('/companies').expect(401);
    });

    it('should reject invalid API key', async () => {
      await request(httpServer)
        .get('/companies')
        .set('Authorization', 'Bearer invalid-key')
        .expect(401);
    });
  });
});
