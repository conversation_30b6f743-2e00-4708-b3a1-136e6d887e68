module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../',
  testRegex: '.e2e-spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/**/*.(t|j)s'],
  coverageDirectory: 'coverage/e2e',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  globalSetup: '<rootDir>/test/e2e-tests/jest-global-setup.ts',
  globalTeardown: '<rootDir>/test/e2e-tests/jest-global-teardown.ts',
  maxWorkers: 1, // Run tests sequentially to avoid database conflicts
}; 