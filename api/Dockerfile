# Node LTS + alpine
FROM node:22-alpine AS dev

RUN apk --update add --no-cache --virtual curl vim nano
RUN npm i -g @nestjs/cli

# Define variables
ENV APP_ROOT=/srv/app
WORKDIR ${APP_ROOT}


COPY [".docker/dev/entrypoint.sh", "${APP_ROOT}/.docker/dev/entrypoint.sh"]
RUN chmod +x .docker/dev/entrypoint.sh
ENTRYPOINT [".docker/dev/entrypoint.sh"]
EXPOSE 3000

# Production build
FROM node:22-alpine AS prod
RUN apk --update add --no-cache --virtual curl vim nano
RUN npm i -g @nestjs/cli

# Define variables
ENV APP_ROOT=/srv/backend
WORKDIR ${APP_ROOT}

COPY ["package.json", "package-lock.json", "${APP_ROOT}/"]
COPY ["src", "${APP_ROOT}/src"]
COPY ["tsconfig.json", "tsconfig.build.json", "${APP_ROOT}/"]
COPY ["nest-cli.json", "${APP_ROOT}/"]
RUN npm ci && npm run build && rm -rf src && npm prune --production

CMD ["npm" , "run" , "start:prod"]
EXPOSE 3000