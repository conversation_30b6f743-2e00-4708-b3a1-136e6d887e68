# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Development
- `npm run build` - Build the NestJS application
- `npm run start` - Start the application
- `npm run start:dev` - Start in development mode with watch
- `npm run start:debug` - Start in debug mode with watch
- `npm run start:prod` - Start in production mode

### Code Quality
- `npm run lint` - Run ESLint with auto-fix
- `npm run lint:check` - Run ESLint without auto-fix (for CI)
- `npm run format` - Format code with Prettier

### Testing
- `npm run test:unit` - Run unit tests
- `npm run test:unit:watch` - Run unit tests in watch mode
- `npm run test:unit:cov` - Run unit tests with coverage
- `npm run test:e2e` - Run end-to-end tests
- `npm run test:e2e:cov` - Run e2e tests with coverage
- `npm run test:debug` - Debug unit tests

## Architecture Overview

This is a NestJS application with a modular architecture that follows domain-driven design principles. The application uses:

- **TypeORM** with PostgreSQL for data persistence
- **NATS JetStream** for event-driven messaging between services
- **Socket.IO** for real-time WebSocket communications
- **Swagger** for API documentation (available at `/api`)

### Core Architecture Patterns

#### Module Structure
Each domain follows this consistent structure:
```
modules/[domain]/
├── controller/           # REST API endpoints
├── entity/              # TypeORM entities
├── service/             # Business logic
├── jetstream-handler/   # NATS JetStream event handlers
├── dto/                 # Data transfer objects
├── enum/                # Domain-specific enums
├── interface/           # TypeScript interfaces
├── factory/             # Object factories
├── gateway/             # WebSocket gateways
└── subscriber/          # Event subscribers
```

#### Key Modules
- **JetstreamModule**: Core event streaming infrastructure using NATS JetStream
- **HealthModule**: Application health checks
- **ItemModule**: Item management with CRUD operations and event handling
- **ItemGroupModule**: Item group management with CRUD operations and event handling
- **MenuModule**: Dynamic menu building functionality
- **PricingModule**: Price management with item-price and price-list entities
- **OrderModule**: Order processing with WebSocket notifications

#### Event-Driven Architecture
The application uses NATS JetStream for inter-service communication:
- Event handlers in `jetstream-handler/` directories process incoming events
- Events are typed using interfaces in `modules/jetstream/interface/`
- Document types and event types are defined in `modules/jetstream/enum/`

#### Database Configuration
- Uses PostgreSQL with TypeORM
- Snake case naming strategy for database columns
- Auto-loads entities from modules
- SSL support configurable via environment variables

## Coding Standards

This project follows strict TypeScript and NestJS conventions as defined in `.cursorrules`:

### Key Principles
- Always declare types for variables and functions (avoid `any`)
- Use English for all code and documentation
- Use JSDoc for public classes and methods
- One export per file
- Functions should be less than 20 instructions with single purpose
- Classes should be less than 200 instructions with less than 10 public methods

### Naming Conventions
- PascalCase for classes
- camelCase for variables, functions, and methods
- kebab-case for file and directory names
- UPPERCASE for environment variables
- Use verbs for functions and boolean variables (isX, hasX, canX)

### Testing Requirements
- Use Jest for both unit and e2e tests
- Follow Arrange-Act-Assert convention for unit tests
- Follow Given-When-Then convention for acceptance tests
- Write tests for each controller and service
- Add admin/test methods to controllers as smoke tests
- Always add e2e tests for the endpoints we introduce

## Environment Setup

Required environment variables:
- `DB_HOST`, `DB_PORT`, `DB_USERNAME`, `DB_PASSWORD`, `DB_NAME` - Database connection
- `DATABASE_SSL` - Enable SSL for database (boolean as string)
- `NODE_ENV` - Environment mode (affects database synchronization)
- `PORT` - Application port (defaults to 3000)

## Architecture and Concept Behind the Project

This project serves as a local caching layer between restaurant-based Point of Sale (POS) systems and a cloud-hosted ERPNext instance. Its primary goal is to ensure the POS remains functional even during internet outages.

Key components of the architecture include:
	•	Data Sync via NATS JetStream:
Any changes made to relevant doctypes in ERPNext are published to NATS JetStream. This NestJS-based application subscribes to those changes and updates the local cache accordingly.
	•	Offline Resilience:
The local cache allows the POS to operate independently of the internet. In the event of a connectivity issue, the POS can continue to function using locally cached data.
	•	Automatic Synchronization:
Once connectivity is restored, the NestJS app consumes any missed events from NATS JetStream to bring the local state back in sync with ERPNext.
	•	POS Integration:
POS clients, built as Electron apps using React, fetch initial data via an API exposed by this application. They also maintain a WebSocket connection to receive real-time updates when relevant data changes.