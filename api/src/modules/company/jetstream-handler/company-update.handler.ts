import { Injectable } from '@nestjs/common';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { Company } from '../entity/company.entity';
import { CompanyService } from '../service/company.service';

@Injectable()
export class CompanyUpdateHandler implements JetstreamMessageHandler<Company> {
  constructor(private readonly companyService: CompanyService) {}
  canHandle(message: JetStreamPayload<Company>): boolean {
    return (
      message.doctype === Doctypes.COMPANY &&
      message.event === EventTypes.UPDATED
    );
  }

  async handle(message: JetStreamPayload<Company>): Promise<void> {
    await this.companyService.createOrUpdateCompany(message.data);
  }
}
