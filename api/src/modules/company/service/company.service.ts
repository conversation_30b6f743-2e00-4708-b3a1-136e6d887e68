import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { Company } from '../entity/company.entity';

@Injectable()
export class CompanyService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
  ) {}

  async createOrUpdateCompany(company: Company): Promise<InsertResult> {
    return this.companyRepository.upsert(company, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteCompany(company: Company): Promise<void> {
    await this.companyRepository.delete(company.name);
  }

  async findCompanies(): Promise<Company[]> {
    return this.companyRepository.find();
  }

  async findCompanyByName(name: string): Promise<Company | null> {
    return this.companyRepository.findOne({ where: { name } });
  }
}
