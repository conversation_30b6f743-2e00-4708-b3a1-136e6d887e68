import { Controller, Get, UseGuards } from '@nestjs/common';
import { CompanyService } from '../service/company.service';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('companies')
@UseGuards(ApiKeyGuard)
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Get()
  async getCompanies() {
    return this.companyService.findCompanies();
  }
}
