import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';

@Entity()
export class Company {
  @PrimaryColumn({ unique: true })
  name: string;

  @Column({ nullable: true })
  doctype?: string;

  @Column({ nullable: true })
  __unsaved?: number;

  @Column({ nullable: true })
  abbr?: string;

  @Column({ nullable: true })
  accumulated_depreciation_account?: string;

  @Column({ nullable: true })
  allow_account_creation_against_child_company?: number;

  @Column({ nullable: true })
  asset_received_but_not_billed?: string;

  @Column({ nullable: true })
  auto_err_frequency?: string;

  @Column({ nullable: true })
  auto_exchange_rate_revaluation?: number;

  @Column({ nullable: true })
  book_advance_payments_in_separate_party_account?: number;

  @Column({ nullable: true })
  capital_work_in_progress_account?: string;

  @Column({ nullable: true })
  chart_of_accounts?: string;

  @Column({ nullable: true })
  company_description?: string;

  @Column({ nullable: true })
  company_logo?: string;

  @Column({ nullable: true })
  company_name?: string;

  @Column({ nullable: true })
  cost_center?: string;

  @Column({ nullable: true })
  country?: string;

  @Column({ nullable: true })
  create_chart_of_accounts_based_on?: string;

  @Column({ nullable: true })
  creation?: string;

  @Column({ nullable: true, type: 'float' })
  credit_limit?: number;

  @Column({ nullable: true })
  date_of_commencement?: string;

  @Column({ nullable: true })
  date_of_establishment?: string;

  @Column({ nullable: true })
  date_of_incorporation?: string;

  @Column({ nullable: true })
  default_advance_paid_account?: string;

  @Column({ nullable: true })
  default_advance_received_account?: string;

  @Column({ nullable: true })
  default_bank_account?: string;

  @Column({ nullable: true })
  default_buying_terms?: string;

  @Column({ nullable: true })
  default_cash_account?: string;

  @Column({ nullable: true })
  default_currency?: string;

  @Column({ nullable: true })
  default_deferred_expense_account?: string;

  @Column({ nullable: true })
  default_deferred_revenue_account?: string;

  @Column({ nullable: true })
  default_discount_account?: string;

  @Column({ nullable: true })
  default_expense_account?: string;

  @Column({ nullable: true })
  default_finance_book?: string;

  @Column({ nullable: true })
  default_holiday_list?: string;

  @Column({ nullable: true })
  default_in_transit_warehouse?: string;

  @Column({ nullable: true })
  default_income_account?: string;

  @Column({ nullable: true })
  default_inventory_account?: string;

  @Column({ nullable: true })
  default_letter_head?: string;

  @Column({ nullable: true })
  default_operating_cost_account?: string;

  @Column({ nullable: true })
  default_payable_account?: string;

  @Column({ nullable: true })
  default_provisional_account?: string;

  @Column({ nullable: true })
  default_receivable_account?: string;

  @Column({ nullable: true })
  default_selling_terms?: string;

  @Column({ nullable: true })
  default_warehouse_for_sales_return?: string;

  @Column({ nullable: true })
  depreciation_cost_center?: string;

  @Column({ nullable: true })
  depreciation_expense_account?: string;

  @Column({ nullable: true })
  disposal_account?: string;

  @Column({ nullable: true })
  docstatus?: number;

  @Column({ nullable: true })
  domain?: string;

  @Column({ nullable: true })
  email?: string;

  @Column({ nullable: true })
  enable_perpetual_inventory?: number;

  @Column({ nullable: true })
  enable_provisional_accounting_for_non_stock_items?: number;

  @Column({ nullable: true })
  exception_budget_approver_role?: string;

  @Column({ nullable: true })
  exchange_gain_loss_account?: string;

  @Column({ nullable: true })
  existing_company?: string;

  @Column({ nullable: true })
  expenses_included_in_asset_valuation?: string;

  @Column({ nullable: true })
  expenses_included_in_valuation?: string;

  @Column({ nullable: true })
  fax?: string;

  @Column({ nullable: true })
  idx?: number;

  @Column({ nullable: true })
  is_group?: number;

  @Column({ nullable: true })
  lft?: number;

  @Column({ nullable: true })
  modified?: string;

  @Column({ nullable: true })
  modified_by?: string;

  @Column({ nullable: true, type: 'float' })
  monthly_sales_target?: number;

  @Column({ nullable: true })
  old_parent?: string;

  @Column({ nullable: true })
  owner?: string;

  @Column({ nullable: true })
  parent_company?: string;

  @Column({ nullable: true })
  payment_terms?: string;

  @Column({ nullable: true })
  phone_no?: string;

  @Column({ nullable: true })
  reconcile_on_advance_payment_date?: number;

  @Column({ nullable: true })
  reconciliation_takes_effect_on?: string;

  @Column({ nullable: true })
  registration_details?: string;

  @Column({ nullable: true })
  rgt?: number;

  @Column({ nullable: true })
  round_off_account?: string;

  @Column({ nullable: true })
  round_off_cost_center?: string;

  @Column({ nullable: true })
  round_off_for_opening?: string;

  @Column({ nullable: true })
  sales_monthly_history?: string;

  @Column({ nullable: true })
  series_for_depreciation_entry?: string;

  @Column({ nullable: true })
  stock_adjustment_account?: string;

  @Column({ nullable: true })
  stock_received_but_not_billed?: string;

  @Column({ nullable: true })
  submit_err_jv?: number;

  @Column({ nullable: true })
  tax_id?: string;

  @Column({ nullable: true, type: 'float' })
  total_monthly_sales?: number;

  @Column({ nullable: true })
  transactions_annual_history?: string;

  @Column({ nullable: true })
  unrealized_exchange_gain_loss_account?: string;

  @Column({ nullable: true })
  unrealized_profit_loss_account?: string;

  @Column({ nullable: true })
  website?: string;

  @Column({ nullable: true })
  write_off_account?: string;
}
