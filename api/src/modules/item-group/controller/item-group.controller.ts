import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ItemGroupService } from '../service/item-group.service';
import { ItemGroup } from '../entity/item-group.entity';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('/item-groups')
@UseGuards(ApiKeyGuard)
export class ItemGroupController {
  constructor(private readonly itemGroupService: ItemGroupService) {}

  @Get()
  async getItemGroups(): Promise<ItemGroup[]> {
    return this.itemGroupService.findAll();
  }

  @Get(':name')
  async getItemGroupByName(@Param('name') name: string): Promise<ItemGroup> {
    return this.itemGroupService.findByName(name);
  }

  @Get(':name/children')
  async getItemGroupChildren(
    @Param('name') parentName: string,
  ): Promise<ItemGroup[]> {
    return this.itemGroupService.findChildren(parentName);
  }
}
