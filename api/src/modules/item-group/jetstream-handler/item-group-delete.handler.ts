import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { ItemGroup } from '../entity/item-group.entity';
import { ItemGroupService } from '../service/item-group.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ItemGroupDeleteHandler
  implements JetstreamMessageHandler<ItemGroup>
{
  constructor(private readonly itemGroupService: ItemGroupService) {}
  canHandle(message: JetStreamPayload<ItemGroup>): boolean {
    return (
      message.doctype === Doctypes.ITEM_GROUP &&
      message.event === EventTypes.DELETED
    );
  }

  async handle(message: JetStreamPayload<ItemGroup>): Promise<void> {
    await this.itemGroupService.deleteItemGroup(message.data.name);
  }
}
