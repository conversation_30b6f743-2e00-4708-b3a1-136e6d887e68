import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { ItemGroup } from '../entity/item-group.entity';
import { EventTypes } from '../../jetstream/enum/event-types';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { Injectable } from '@nestjs/common';
import { ItemGroupService } from '../service/item-group.service';

@Injectable()
export class ItemGroupUpdateHandler
  implements JetstreamMessageHandler<ItemGroup>
{
  constructor(private readonly itemGroupService: ItemGroupService) {}
  canHandle(message: JetStreamPayload<ItemGroup>): boolean {
    return (
      message.doctype === Doctypes.ITEM_GROUP &&
      message.event === EventTypes.UPDATED
    );
  }

  async handle(message: JetStreamPayload<ItemGroup>): Promise<void> {
    await this.itemGroupService.createOrUpdateItemGroup(message.data);
  }
}
