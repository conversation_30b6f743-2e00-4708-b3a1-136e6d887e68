import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ItemGroup } from './entity/item-group.entity';
import { ItemGroupUpdateHandler } from './jetstream-handler/item-group-update.handler';
import { ItemGroupService } from './service/item-group.service';
import { ItemGroupController } from './controller/item-group.controller';
import { ItemGroupDeleteHandler } from './jetstream-handler/item-group-delete.handler';

@Module({
  imports: [TypeOrmModule.forFeature([ItemGroup])],
  controllers: [ItemGroupController],
  providers: [ItemGroupService, ItemGroupUpdateHandler, ItemGroupDeleteHandler],
  exports: [ItemGroupService, ItemGroupUpdateHandler, ItemGroupDeleteHandler],
})
export class ItemGroupModule {}
