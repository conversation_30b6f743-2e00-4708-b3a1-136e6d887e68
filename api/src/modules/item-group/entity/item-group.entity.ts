import { Column, <PERSON>ti<PERSON>, PrimaryColumn } from 'typeorm';

@Entity()
export class ItemGroup {
  @PrimaryColumn({ unique: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  __unsaved?: number;

  @Column({ nullable: true })
  creation?: string;

  @Column({ nullable: true })
  docstatus?: number;

  @Column({ nullable: true })
  doctype?: string;

  @Column({ nullable: true })
  idx?: number;

  @Column({ nullable: true })
  image?: string;

  @Column({ nullable: true })
  is_group?: number;

  @Column('jsonb', { nullable: true })
  item_group_defaults?: any[];

  @Column({ nullable: true })
  item_group_name?: string;

  @Column({ nullable: true })
  lft?: number;

  @Column({ nullable: true })
  modified?: string;

  @Column({ nullable: true })
  modified_by?: string;

  @Column({ nullable: true })
  old_parent?: string;

  @Column({ nullable: true })
  owner?: string;

  @Column({ nullable: true })
  parent_item_group?: string;

  @Column({ nullable: true })
  rgt?: number;

  @Column('jsonb', { nullable: true })
  taxes?: any[];
}
