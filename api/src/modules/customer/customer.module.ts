import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Customer } from './entity/customer.entity';
import { CustomerService } from './service/customer.service';
import { CustomerController } from './controller/customer.controller';
import { CustomerUpdateHandler } from './jetstream-handler/customer-update.handler';
import { CustomerDeleteHandler } from './jetstream-handler/customer-delete.handler';

@Module({
  imports: [TypeOrmModule.forFeature([Customer])],
  controllers: [CustomerController],
  providers: [CustomerService, CustomerUpdateHandler, CustomerDeleteHandler],
  exports: [CustomerService, CustomerUpdateHandler, CustomerDeleteHandler],
})
export class CustomerModule {}
