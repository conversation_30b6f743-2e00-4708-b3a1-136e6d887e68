import { Controller, Get, UseGuards } from '@nestjs/common';
import { CustomerService } from '../service/customer.service';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('customers')
@UseGuards(ApiKeyGuard)
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Get()
  async getCustomers() {
    return this.customerService.findCustomers();
  }
}
