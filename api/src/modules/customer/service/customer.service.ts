import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { Customer } from '../entity/customer.entity';

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
  ) {}

  async createOrUpdateCustomer(customer: Customer): Promise<InsertResult> {
    return this.customerRepository.upsert(customer, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteCustomer(customer: Customer): Promise<void> {
    await this.customerRepository.delete(customer.name);
  }

  async findCustomers(): Promise<Customer[]> {
    return this.customerRepository.find();
  }

  async findCustomerByName(name: string): Promise<Customer | null> {
    return this.customerRepository.findOne({ where: { name } });
  }
}
