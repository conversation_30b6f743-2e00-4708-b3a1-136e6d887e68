import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';

@Entity()
export class Customer {
  @PrimaryColumn({ unique: true })
  name: string;

  @Column({ nullable: true })
  doctype?: string;

  @Column({ nullable: true })
  __run_link_triggers?: number;

  @Column({ nullable: true })
  __unsaved?: number;

  @Column({ nullable: true })
  account_manager?: string;

  @Column('jsonb', { nullable: true })
  accounts?: any[];

  @Column('jsonb', { nullable: true })
  companies?: any[];

  @Column({ nullable: true })
  creation?: string;

  @Column('jsonb', { nullable: true })
  credit_limits?: any[];

  @Column({ nullable: true })
  customer_details?: string;

  @Column({ nullable: true })
  customer_group?: string;

  @Column({ nullable: true })
  customer_name?: string;

  @Column({ nullable: true })
  customer_pos_id?: string;

  @Column({ nullable: true })
  customer_primary_address?: string;

  @Column({ nullable: true })
  customer_primary_contact?: string;

  @Column({ nullable: true })
  customer_type?: string;

  @Column({ nullable: true })
  default_bank_account?: string;

  @Column({ nullable: true, type: 'float' })
  default_commission_rate?: number;

  @Column({ nullable: true })
  default_currency?: string;

  @Column({ nullable: true })
  default_price_list?: string;

  @Column({ nullable: true })
  default_sales_partner?: string;

  @Column({ nullable: true })
  disabled?: number;

  @Column({ nullable: true })
  dn_required?: number;

  @Column({ nullable: true })
  docstatus?: number;

  @Column({ nullable: true })
  email_id?: string;

  @Column({ nullable: true })
  gender?: string;

  @Column({ nullable: true })
  idx?: number;

  @Column({ nullable: true })
  image?: string;

  @Column({ nullable: true })
  industry?: string;

  @Column({ nullable: true })
  is_frozen?: number;

  @Column({ nullable: true })
  is_internal_customer?: number;

  @Column({ nullable: true })
  language?: string;

  @Column({ nullable: true })
  lead_name?: string;

  @Column({ nullable: true })
  loyalty_program?: string;

  @Column({ nullable: true })
  loyalty_program_tier?: string;

  @Column({ nullable: true })
  market_segment?: string;

  @Column({ nullable: true })
  mobile_no?: string;

  @Column({ nullable: true })
  modified?: string;

  @Column({ nullable: true })
  modified_by?: string;

  @Column({ nullable: true })
  naming_series?: string;

  @Column({ nullable: true })
  opportunity_name?: string;

  @Column({ nullable: true })
  owner?: string;

  @Column({ nullable: true })
  payment_terms?: string;

  @Column('jsonb', { nullable: true })
  portal_users?: any[];

  @Column({ nullable: true })
  primary_address?: string;

  @Column({ nullable: true })
  prospect_name?: string;

  @Column({ nullable: true })
  represents_company?: string;

  @Column('jsonb', { nullable: true })
  sales_team?: any[];

  @Column({ nullable: true })
  salutation?: string;

  @Column({ nullable: true })
  so_required?: number;

  @Column({ nullable: true })
  tax_category?: string;

  @Column({ nullable: true })
  tax_id?: string;

  @Column({ nullable: true })
  tax_withholding_category?: string;

  @Column({ nullable: true })
  territory?: string;

  @Column({ nullable: true })
  website?: string;
}
