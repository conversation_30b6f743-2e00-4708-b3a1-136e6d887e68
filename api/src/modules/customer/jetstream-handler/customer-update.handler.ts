import { Injectable } from '@nestjs/common';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { Customer } from '../entity/customer.entity';
import { CustomerService } from '../service/customer.service';

@Injectable()
export class CustomerUpdateHandler
  implements JetstreamMessageHandler<Customer>
{
  constructor(private readonly customerService: CustomerService) {}
  canHandle(message: JetStreamPayload<Customer>): boolean {
    return (
      message.doctype === Doctypes.CUSTOMER &&
      message.event === EventTypes.UPDATED
    );
  }

  async handle(message: JetStreamPayload<Customer>): Promise<void> {
    await this.customerService.createOrUpdateCustomer(message.data);
  }
}
