import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateOrderDto } from '../dto/createOrder.dto';
import { Order } from '../entity/order.entity';

@Injectable()
export class OrderFactory {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}
  async createOrder(createOrderDto: CreateOrderDto): Promise<Order> {
    const order = new Order();
    order.name = createOrderDto.name;
    order.status = createOrderDto.status;
    await this.orderRepository.save(order);
    return order;
  }
}
