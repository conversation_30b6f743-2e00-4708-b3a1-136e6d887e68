import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { OrderEvents } from '../enum/orderEvents.enum';
import { Order } from '../entity/order.entity';
import { OrderGateway } from '../gateway/order.gateway';

@Injectable()
export class OrderCreatedLocalWebsocketSubscriber {
  constructor(private readonly orderGateway: OrderGateway) {}

  @OnEvent(OrderEvents.ORDER_CREATED)
  onOrderCreated(order: Order): void {
    this.orderGateway.notifyUsers(order);
  }
}
