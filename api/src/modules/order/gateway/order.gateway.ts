import { Logger } from '@nestjs/common';
import {
  WebSocketGateway,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
} from '@nestjs/websockets';
import { Socket, Server } from 'socket.io';
import { OrderEvents } from '../enum/orderEvents.enum';
import { Order } from '../entity/order.entity';

@WebSocketGateway({
  cors: {
    origin: ['http://localhost:8081'],
    credentials: true,
  },
  namespace: 'orders',
})
export class OrderGateway implements OnGatewayConnection, OnGatewayDisconnect {
  private readonly logger = new Logger(OrderGateway.name);

  @WebSocketServer()
  server: Server;

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    client.emit('message', 'Welcome to the order service');
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  notifyUsers(order: Order): void {
    this.logger.log(`Broadcasting order created event: ${order.id}`);
    this.server.emit(OrderEvents.ORDER_CREATED, order);
  }
}
