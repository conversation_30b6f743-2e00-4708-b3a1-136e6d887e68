import {
  Controller,
  Get,
  Param,
  Post,
  Body,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { OrderService } from '../service/order.service';
import { Order } from '../entity/order.entity';
import { CreateOrderDto } from '../dto/createOrder.dto';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('orders')
@UseGuards(ApiKeyGuard)
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  async findAll(): Promise<Order[]> {
    return this.orderService.findAll();
  }

  @Get(':id')
  async findById(@Param('id') id: string): Promise<Order> {
    return this.orderService.findById(id);
  }

  @Post()
  async create(@Body() createOrderDto: CreateOrderDto): Promise<Order> {
    return this.orderService.create(createOrderDto);
  }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<void> {
    return this.orderService.delete(id);
  }
}
