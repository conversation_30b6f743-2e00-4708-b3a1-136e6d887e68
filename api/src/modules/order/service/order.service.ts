import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateOrderDto } from '../dto/createOrder.dto';
import { Order } from '../entity/order.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderEvents } from '../enum/orderEvents.enum';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { OrderFactory } from '../factory/order.factory';
@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly eventEmitter: EventEmitter2,
    private readonly orderFactory: OrderFactory,
  ) {}

  async findAll(): Promise<Order[]> {
    return this.orderRepository.find();
  }

  async findById(id: string): Promise<Order> {
    const order = await this.orderRepository.findOneBy({ id });
    if (!order) {
      throw new NotFoundException('Order not found');
    }
    return order;
  }

  async create(createOrderDto: CreateOrderDto): Promise<Order> {
    const order = await this.orderFactory.createOrder(createOrderDto);
    this.eventEmitter.emit(OrderEvents.ORDER_CREATED, order);
    return order;
  }

  async delete(id: string): Promise<void> {
    const order = await this.orderRepository.findOneBy({ id });
    if (!order) {
      throw new NotFoundException('Order not found');
    }
    await this.orderRepository.delete(id);
    this.eventEmitter.emit(OrderEvents.ORDER_DELETED, order);
  }
}
