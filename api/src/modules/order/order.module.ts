import { Modu<PERSON> } from '@nestjs/common';
import { OrderGateway } from './gateway/order.gateway';
import { OrderService } from './service/order.service';
import { OrderController } from './controller/order.controller';
import { OrderCreatedLocalWebsocketSubscriber } from './subscriber/order.created.localWebsocket.subscriber';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from './entity/order.entity';
import { OrderFactory } from './factory/order.factory';
@Module({
  imports: [TypeOrmModule.forFeature([Order])],
  controllers: [OrderController],
  providers: [
    OrderGateway,
    OrderService,
    OrderCreatedLocalWebsocketSubscriber,
    OrderFactory,
  ],
})
export class OrderModule {}
