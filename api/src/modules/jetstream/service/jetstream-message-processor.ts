import { Injectable, Logger } from '@nestjs/common';
import { JetStreamPayload } from '../interface/jetstream-payload';
import { ItemGroupUpdateHandler } from 'src/modules/item-group/jetstream-handler/item-group-update.handler';
import { JetstreamMessageHandler } from '../interface/jetstream-message-handler';
import { ItemGroupDeleteHandler } from 'src/modules/item-group/jetstream-handler/item-group-delete.handler';
import { ItemUpdateHandler } from 'src/modules/item/jetstream-handler/item-update.handler';
import { ItemDeleteHandler } from 'src/modules/item/jetstream-handler/item-delete.handler';
import { PriceListUpdateHandler } from 'src/modules/pricing/jetstream-handler/price-list-update.handler';
import { PriceListDeleteHandler } from 'src/modules/pricing/jetstream-handler/price-list-delete.handler';
import { ItemPriceUpdateHandler } from 'src/modules/pricing/jetstream-handler/item-price-update.handler';
import { ItemPriceDeleteHandler } from 'src/modules/pricing/jetstream-handler/item-price-delete.handler';
import { PosProfileUpdateHandler } from 'src/modules/pos-profile/jetstream-handler/pos-profile-update.handler';
import { PosProfileDeleteHandler } from 'src/modules/pos-profile/jetstream-handler/pos-profile-delete.handler';
import { CustomerUpdateHandler } from 'src/modules/customer/jetstream-handler/customer-update.handler';
import { CustomerDeleteHandler } from 'src/modules/customer/jetstream-handler/customer-delete.handler';
import { ModeOfPaymentUpdateHandler } from 'src/modules/mode-of-payment/jetstream-handler/mode-of-payment-update.handler';
import { ModeOfPaymentDeleteHandler } from 'src/modules/mode-of-payment/jetstream-handler/mode-of-payment-delete.handler';
import { CompanyUpdateHandler } from 'src/modules/company/jetstream-handler/company-update.handler';
import { CompanyDeleteHandler } from 'src/modules/company/jetstream-handler/company-delete.handler';

@Injectable()
export class JetstreamMessageProcessor {
  private readonly messageHandlers: JetstreamMessageHandler[] = [];
  private readonly logger = new Logger(JetstreamMessageProcessor.name);
  constructor(
    private readonly itemGroupUpdateHandler: ItemGroupUpdateHandler,
    private readonly itemGroupDeleteHandler: ItemGroupDeleteHandler,
    private readonly itemUpdateHandler: ItemUpdateHandler,
    private readonly itemDeleteHandler: ItemDeleteHandler,
    private readonly priceListUpdateHandler: PriceListUpdateHandler,
    private readonly priceListDeleteHandler: PriceListDeleteHandler,
    private readonly itemPriceUpdateHandler: ItemPriceUpdateHandler,
    private readonly itemPriceDeleteHandler: ItemPriceDeleteHandler,
    private readonly posProfileUpdateHandler: PosProfileUpdateHandler,
    private readonly posProfileDeleteHandler: PosProfileDeleteHandler,
    private readonly customerUpdateHandler: CustomerUpdateHandler,
    private readonly customerDeleteHandler: CustomerDeleteHandler,
    private readonly modeOfPaymentUpdateHandler: ModeOfPaymentUpdateHandler,
    private readonly modeOfPaymentDeleteHandler: ModeOfPaymentDeleteHandler,
    private readonly companyUpdateHandler: CompanyUpdateHandler,
    private readonly companyDeleteHandler: CompanyDeleteHandler,
  ) {
    this.messageHandlers.push(this.itemGroupUpdateHandler);
    this.messageHandlers.push(this.itemGroupDeleteHandler);
    this.messageHandlers.push(this.itemUpdateHandler);
    this.messageHandlers.push(this.itemDeleteHandler);
    this.messageHandlers.push(this.priceListUpdateHandler);
    this.messageHandlers.push(this.priceListDeleteHandler);
    this.messageHandlers.push(this.itemPriceUpdateHandler);
    this.messageHandlers.push(this.itemPriceDeleteHandler);
    this.messageHandlers.push(this.posProfileUpdateHandler);
    this.messageHandlers.push(this.posProfileDeleteHandler);
    this.messageHandlers.push(this.customerUpdateHandler);
    this.messageHandlers.push(this.customerDeleteHandler);
    this.messageHandlers.push(this.modeOfPaymentUpdateHandler);
    this.messageHandlers.push(this.modeOfPaymentDeleteHandler);
    this.messageHandlers.push(this.companyUpdateHandler);
    this.messageHandlers.push(this.companyDeleteHandler);
  }

  async processMessage(message: JetStreamPayload<any>): Promise<void> {
    const handler = this.messageHandlers.find((handler) =>
      handler.canHandle(message),
    );
    if (handler) {
      this.logger.log('handler found : ' + handler.constructor.name);
      await handler.handle(message);
    } else {
      this.logger.warn(
        `No handler found for message: ${JSON.stringify(message)}`,
      );
    }
  }
}
