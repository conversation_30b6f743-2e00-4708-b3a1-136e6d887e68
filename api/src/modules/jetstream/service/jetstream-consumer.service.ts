import {
  Injectable,
  Logger,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import {
  connect,
  consumerOpts,
  JetStreamClient,
  JetStreamSubscription,
  NatsConnection,
  StringCodec,
} from 'nats';
import { JetStreamPayload } from '../interface/jetstream-payload';
import { JetstreamMessageProcessor } from './jetstream-message-processor';

/**
 * Service to consume messages from NATS JetStream.
 */
@Injectable()
export class JetstreamConsumerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(JetstreamConsumerService.name);
  private natsConnection!: NatsConnection;
  private jetStreamClient!: JetStreamClient;
  private subscription?: JetStreamSubscription;

  constructor(
    private readonly jetstreamMessageProcessor: JetstreamMessageProcessor,
  ) {}

  /**
   * Initializes the NATS connection and JetStream client.
   */
  async onModuleInit(): Promise<void> {
    this.natsConnection = await connect({ servers: process.env.NATS_URL });
    this.jetStreamClient = this.natsConnection.jetstream();
    await this.subscribeToStream();
  }

  /**
   * Subscribes to the SABA_STREAM_Item stream and processes messages.
   */
  private async subscribeToStream(): Promise<void> {
    const subject = process.env.NATS_SUBJECT;
    this.logger.log(`Subscribing to subject: ${subject}`);
    const durableName = process.env.NATS_DURABLE_CONSUMER_NAME;
    const deliverTo = process.env.NATS_DURABLE_CONSUMER_DELIVER_TO;
    const opts = consumerOpts();
    opts.durable(durableName!);
    opts.manualAck();
    opts.ackExplicit();
    opts.deliverTo(deliverTo!);
    opts.filterSubject(subject!);

    this.subscription = await this.jetStreamClient.subscribe(subject!, opts);
    const stringCodec = StringCodec();

    (async () => {
      for await (const message of this.subscription!) {
        const data = stringCodec.decode(message.data);
        this.logger.log(`Received message: ${data}`);
        try {
          const parsed = JSON.parse(data) as JetStreamPayload;
          await this.jetstreamMessageProcessor.processMessage(parsed);
        } catch (err) {
          this.logger.error('Error processing message', err);
        }
        message.ack();
      }
    })().catch((err) => this.logger.error('Subscription error', err));
  }

  /**
   * Closes the NATS connection on module destroy.
   */
  async onModuleDestroy(): Promise<void> {
    await this.subscription?.destroy();
    await this.natsConnection?.drain();
  }
}
