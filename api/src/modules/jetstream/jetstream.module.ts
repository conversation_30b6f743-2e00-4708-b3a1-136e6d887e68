import { Modu<PERSON> } from '@nestjs/common';
import { ItemGroupModule } from '../item-group/item-group.module';
import { JetstreamConsumerService } from './service/jetstream-consumer.service';
import { JetstreamMessageProcessor } from './service/jetstream-message-processor';
import { ItemModule } from '../item/item.module';
import { PricingModule } from '../pricing/pricing.module';
import { PosProfileModule } from '../pos-profile/pos-profile.module';
import { CustomerModule } from '../customer/customer.module';
import { ModeOfPaymentModule } from '../mode-of-payment/mode-of-payment.module';
import { CompanyModule } from '../company/company.module';
@Module({
  imports: [
    ItemGroupModule,
    ItemModule,
    PricingModule,
    PosProfileModule,
    CustomerModule,
    ModeOfPaymentModule,
    CompanyModule,
  ],
  controllers: [],
  providers: [JetstreamConsumerService, JetstreamMessageProcessor],
})
export class JetstreamModule {}
