import { Controller, Get, UseGuards } from '@nestjs/common';
import { ModeOfPaymentService } from '../service/mode-of-payment.service';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('modes-of-payment')
@UseGuards(ApiKeyGuard)
export class ModeOfPaymentController {
  constructor(private readonly modeOfPaymentService: ModeOfPaymentService) {}

  @Get()
  async getModesOfPayment() {
    return this.modeOfPaymentService.findModeOfPayments();
  }
}
