import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { ModeOfPayment } from '../entity/mode-of-payment.entity';

@Injectable()
export class ModeOfPaymentService {
  constructor(
    @InjectRepository(ModeOfPayment)
    private readonly modeOfPaymentRepository: Repository<ModeOfPayment>,
  ) {}

  async createOrUpdateModeOfPayment(
    modeOfPayment: ModeOfPayment,
  ): Promise<InsertResult> {
    return this.modeOfPaymentRepository.upsert(modeOfPayment, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteModeOfPayment(modeOfPayment: ModeOfPayment): Promise<void> {
    await this.modeOfPaymentRepository.delete(modeOfPayment.name);
  }

  async findModeOfPayments(): Promise<ModeOfPayment[]> {
    return this.modeOfPaymentRepository.find();
  }

  async findModeOfPaymentByName(name: string): Promise<ModeOfPayment | null> {
    return this.modeOfPaymentRepository.findOne({ where: { name } });
  }
}
