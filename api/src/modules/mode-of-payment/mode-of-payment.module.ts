import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ModeOfPayment } from './entity/mode-of-payment.entity';
import { ModeOfPaymentService } from './service/mode-of-payment.service';
import { ModeOfPaymentController } from './controller/mode-of-payment.controller';
import { ModeOfPaymentUpdateHandler } from './jetstream-handler/mode-of-payment-update.handler';
import { ModeOfPaymentDeleteHandler } from './jetstream-handler/mode-of-payment-delete.handler';

@Module({
  imports: [TypeOrmModule.forFeature([ModeOfPayment])],
  controllers: [ModeOfPaymentController],
  providers: [
    ModeOfPaymentService,
    ModeOfPaymentUpdateHandler,
    ModeOfPaymentDeleteHandler,
  ],
  exports: [
    ModeOfPaymentService,
    ModeOfPaymentUpdateHandler,
    ModeOfPaymentDeleteHandler,
  ],
})
export class ModeOfPaymentModule {}
