import { Column, <PERSON>tity, PrimaryColumn } from 'typeorm';

@Entity()
export class ModeOfPayment {
  @PrimaryColumn({ unique: true })
  name: string;

  @Column({ nullable: true })
  doctype?: string;

  @Column({ nullable: true })
  __run_link_triggers?: number;

  @Column({ nullable: true })
  __unsaved?: number;

  @Column('jsonb', { nullable: true })
  accounts?: any[];

  @Column({ nullable: true })
  creation?: string;

  @Column({ nullable: true })
  docstatus?: number;

  @Column({ nullable: true })
  enabled?: number;

  @Column({ nullable: true })
  idx?: number;

  @Column({ nullable: true })
  mode_of_payment?: string;

  @Column({ nullable: true })
  modified?: string;

  @Column({ nullable: true })
  modified_by?: string;

  @Column({ nullable: true })
  owner?: string;

  @Column({ nullable: true })
  type?: string;
}
