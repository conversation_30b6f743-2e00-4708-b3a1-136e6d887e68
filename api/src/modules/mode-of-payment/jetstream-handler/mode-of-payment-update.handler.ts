import { Injectable } from '@nestjs/common';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { ModeOfPayment } from '../entity/mode-of-payment.entity';
import { ModeOfPaymentService } from '../service/mode-of-payment.service';

@Injectable()
export class ModeOfPaymentUpdateHandler
  implements JetstreamMessageHandler<ModeOfPayment>
{
  constructor(private readonly modeOfPaymentService: ModeOfPaymentService) {}
  canHandle(message: JetStreamPayload<ModeOfPayment>): boolean {
    return (
      message.doctype === Doctypes.MODE_OF_PAYMENT &&
      message.event === EventTypes.UPDATED
    );
  }

  async handle(message: JetStreamPayload<ModeOfPayment>): Promise<void> {
    await this.modeOfPaymentService.createOrUpdateModeOfPayment(message.data);
  }
}
