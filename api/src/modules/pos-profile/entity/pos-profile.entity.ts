import { Column, <PERSON><PERSON>ty, PrimaryColumn } from 'typeorm';

export interface PosProfileUser {
  __unsaved?: number;
  creation?: string;
  default?: number;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  modified?: string;
  modified_by?: string;
  name?: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
  user?: string | null;
}

export interface PosCustomerGroup {
  __unsaved?: number;
  creation?: string;
  customer_group?: string;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  modified?: string;
  modified_by?: string;
  name?: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
}

export interface PosPaymentMethod {
  __unsaved?: number;
  allow_in_returns?: number;
  creation?: string;
  default?: number;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  mode_of_payment?: string;
  modified?: string;
  modified_by?: string;
  name?: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
}

@Entity()
export class PosProfile {
  @PrimaryColumn({ unique: true })
  name: string;
  @Column({ nullable: true })
  __unsaved?: number;
  @Column({ nullable: true })
  account_for_change_amount?: string;
  @Column({ nullable: true })
  allow_discount_change?: number;
  @Column({ nullable: true })
  allow_rate_change?: number;
  @Column('jsonb', { nullable: true })
  applicable_for_users?: PosProfileUser[];
  @Column({ nullable: true })
  apply_discount_on?: string;
  @Column({ nullable: true })
  auto_add_item_to_cart?: number;
  @Column({ nullable: true })
  campaign?: string;
  @Column({ nullable: true })
  company?: string;
  @Column({ nullable: true })
  company_address?: string;
  @Column({ nullable: true })
  cost_center?: string;
  @Column({ nullable: true })
  country?: string;
  @Column({ nullable: true })
  creation?: string;
  @Column({ nullable: true })
  currency?: string;
  @Column({ nullable: true })
  customer?: string;
  @Column('jsonb', { nullable: true })
  customer_groups?: PosCustomerGroup[];
  @Column({ nullable: true })
  disable_grand_total_to_default_mop?: number;
  @Column({ nullable: true })
  disable_rounded_total?: number;
  @Column({ nullable: true })
  disabled?: number;
  @Column({ nullable: true })
  docstatus?: number;
  @Column({ nullable: true })
  doctype?: string;
  @Column({ nullable: true })
  expense_account?: string;
  @Column({ nullable: true })
  hide_images?: number;
  @Column({ nullable: true })
  hide_unavailable_items?: number;
  @Column({ nullable: true })
  idx?: number;
  @Column({ nullable: true })
  ignore_pricing_rule?: number;
  @Column({ nullable: true })
  income_account?: string;
  @Column('jsonb', { nullable: true })
  item_groups?: any[];
  @Column({ nullable: true })
  letter_head?: string;
  @Column({ nullable: true })
  modified?: string;
  @Column({ nullable: true })
  modified_by?: string;
  @Column({ nullable: true })
  owner?: string;
  @Column('jsonb', { nullable: true })
  payments?: PosPaymentMethod[];
  @Column({ nullable: true })
  print_format?: string;
  @Column({ nullable: true })
  print_receipt_on_order_complete?: number;
  @Column({ nullable: true })
  select_print_heading?: string;
  @Column({ nullable: true })
  selling_price_list?: string;
  @Column({ nullable: true })
  tax_category?: string;
  @Column({ nullable: true })
  taxes_and_charges?: string;
  @Column({ nullable: true })
  tc_name?: string;
  @Column({ nullable: true })
  update_stock?: number;
  @Column({ nullable: true })
  validate_stock_on_save?: number;
  @Column({ nullable: true })
  warehouse?: string;
  @Column({ nullable: true })
  write_off_account?: string;
  @Column({ nullable: true })
  write_off_cost_center?: string;
  @Column({ nullable: true, type: 'float' })
  write_off_limit?: number;
}
