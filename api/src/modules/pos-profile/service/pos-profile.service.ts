import { Injectable } from '@nestjs/common';
import { DeleteResult, InsertResult, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PosProfile } from '../entity/pos-profile.entity';

@Injectable()
export class PosProfileService {
  constructor(
    @InjectRepository(PosProfile)
    private readonly posProfileRepository: Repository<PosProfile>,
  ) {}

  async createOrUpdatePosProfile(
    posProfile: PosProfile,
  ): Promise<InsertResult> {
    return this.posProfileRepository.upsert(posProfile, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deletePosProfile(posProfile: PosProfile): Promise<DeleteResult> {
    return this.posProfileRepository.delete({ name: posProfile.name });
  }

  async findAllPosProfiles(): Promise<PosProfile[]> {
    return this.posProfileRepository.find();
  }

  async findPosProfileByName(name: string): Promise<PosProfile | null> {
    const result = await this.posProfileRepository.findOne({ where: { name } });
    return result ?? null;
  }
}
