import { Injectable } from '@nestjs/common';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { PosProfile } from '../entity/pos-profile.entity';
import { PosProfileService } from '../service/pos-profile.service';

@Injectable()
export class PosProfileDeleteHandler
  implements JetstreamMessageHandler<PosProfile>
{
  constructor(private readonly posProfileService: PosProfileService) {}

  canHandle(payload: JetStreamPayload<PosProfile>): boolean {
    return (
      payload.doctype === Doctypes.POS_PROFILE &&
      payload.event === EventTypes.DELETED
    );
  }

  async handle(payload: JetStreamPayload<PosProfile>): Promise<void> {
    await this.posProfileService.deletePosProfile(payload.data);
  }
}
