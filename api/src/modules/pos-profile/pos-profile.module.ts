import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PosProfile } from './entity/pos-profile.entity';
import { PosProfileService } from './service/pos-profile.service';
import { PosProfileController } from './controller/pos-profile.controller';
import { PosProfileUpdateHandler } from './jetstream-handler/pos-profile-update.handler';
import { PosProfileDeleteHandler } from './jetstream-handler/pos-profile-delete.handler';

@Module({
  imports: [TypeOrmModule.forFeature([PosProfile])],
  controllers: [PosProfileController],
  providers: [
    PosProfileService,
    PosProfileUpdateHandler,
    PosProfileDeleteHandler,
  ],
  exports: [
    PosProfileService,
    PosProfileUpdateHandler,
    PosProfileDeleteHandler,
  ],
})
export class PosProfileModule {}
