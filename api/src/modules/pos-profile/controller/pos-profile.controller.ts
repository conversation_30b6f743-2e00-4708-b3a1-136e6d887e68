import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { PosProfileService } from '../service/pos-profile.service';
import { PosProfile } from '../entity/pos-profile.entity';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('pos-profiles')
@UseGuards(ApiKeyGuard)
export class PosProfileController {
  constructor(private readonly posProfileService: PosProfileService) {}

  @Get()
  async getPosProfiles(): Promise<PosProfile[]> {
    return this.posProfileService.findAllPosProfiles();
  }

  @Get(':name')
  async getPosProfileByName(
    @Param('name') name: string,
  ): Promise<PosProfile | null> {
    return this.posProfileService.findPosProfileByName(name);
  }
}
