import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';

@Entity()
export class ItemPrice {
  @PrimaryColumn()
  name?: string;
  @Column({ nullable: true })
  __run_link_triggers?: number;
  @Column({ nullable: true })
  __unsaved?: number;
  @Column({ nullable: true })
  batch_no?: string;
  @Column({ nullable: true })
  brand?: string;
  @Column({ nullable: true })
  buying?: number;
  @Column({ nullable: true })
  creation?: string;
  @Column({ nullable: true })
  currency?: string;
  @Column({ nullable: true })
  customer?: string;
  @Column({ nullable: true })
  docstatus?: number;
  @Column({ nullable: true })
  doctype?: string;
  @Column({ nullable: true })
  idx?: number;
  @Column({ nullable: true })
  item_code?: string;
  @Column({ nullable: true })
  item_description?: string;
  @Column({ nullable: true })
  item_name?: string;
  @Column({ nullable: true })
  lead_time_days?: number;
  @Column({ nullable: true })
  modified?: string;
  @Column({ nullable: true })
  modified_by?: string;
  @Column({ nullable: true })
  note?: string;
  @Column({ nullable: true })
  owner?: string;
  @Column({ nullable: true })
  packing_unit?: number;
  @Column({ nullable: true })
  price_list?: string;
  @Column({ nullable: true, type: 'float' })
  price_list_rate?: number;
  @Column({ nullable: true })
  reference?: string;
  @Column({ nullable: true })
  selling?: number;
  @Column({ nullable: true })
  supplier?: string;
  @Column({ nullable: true })
  uom?: string;
  @Column({ nullable: true })
  valid_from?: string;
  @Column({ nullable: true })
  valid_upto?: string;
}
