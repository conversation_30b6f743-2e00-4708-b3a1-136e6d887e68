import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';

export interface PriceListCountry {
  __unsaved?: number;
  country: string;
  creation: string;
  docstatus: number;
  doctype: string;
  idx: number;
  modified: string;
  modified_by: string;
  name: string;
  owner: string;
  parent: string;
  parentfield: string;
  parenttype: string;
}

@Entity()
export class PriceList {
  @PrimaryColumn({ unique: true })
  name: string;
  @Column({ nullable: true })
  creation?: string;
  @Column({ nullable: true })
  docstatus?: number;
  @Column({ nullable: true })
  doctype?: string;
  @Column({ nullable: true })
  idx?: number;
  @Column({ nullable: true })
  modified?: string;
  @Column({ nullable: true })
  modifiedBy?: string;
  @Column({ nullable: true })
  owner?: string;
  @Column({ nullable: true })
  priceListName?: string;
  @Column({ nullable: true })
  currency?: string;
  @Column({ nullable: true })
  enabled?: number;
  @Column({ nullable: true })
  priceNotUomDependent?: number;
  @Column({ nullable: true })
  buying?: number;
  @Column({ nullable: true })
  selling?: number;
  @Column({ nullable: true })
  isUnsaved?: number;
  @Column('jsonb', { nullable: true })
  countries?: PriceListCountry[];
}
