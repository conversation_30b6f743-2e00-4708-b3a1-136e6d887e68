import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PriceList } from './entity/price-list.entity';
import { PriceListUpdateHandler } from './jetstream-handler/price-list-update.handler';
import { PricingService } from './service/pricing.service';
import { PriceListDeleteHandler } from './jetstream-handler/price-list-delete.handler';
import { ItemPrice } from './entity/item-price.entity';
import { ItemPriceUpdateHandler } from './jetstream-handler/item-price-update.handler';
import { ItemPriceDeleteHandler } from './jetstream-handler/item-price-delete.handler';
import { PricingController } from './controller/pricing.controller';

@Module({
  imports: [TypeOrmModule.forFeature([PriceList, ItemPrice])],
  controllers: [PricingController],
  providers: [
    PricingService,
    PriceListUpdateHandler,
    PriceListDeleteHandler,
    ItemPriceUpdateHandler,
    ItemPriceDeleteHandler,
  ],
  exports: [
    PricingService,
    PriceListUpdateHandler,
    PriceListDeleteHandler,
    ItemPriceUpdateHandler,
    ItemPriceDeleteHandler,
  ],
})
export class PricingModule {}
