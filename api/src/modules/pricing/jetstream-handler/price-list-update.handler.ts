import { Injectable } from '@nestjs/common';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { PriceList } from '../entity/price-list.entity';
import { PricingService } from '../service/pricing.service';

@Injectable()
export class PriceListUpdateHandler {
  constructor(private readonly pricingService: PricingService) {}

  canHandle(payload: JetStreamPayload<PriceList>): boolean {
    return (
      payload.doctype === Doctypes.PRICE_LIST &&
      payload.event === EventTypes.UPDATED
    );
  }

  async handle(payload: JetStreamPayload<PriceList>): Promise<void> {
    await this.pricingService.createOrUpdatePriceList(payload.data);
  }
}
