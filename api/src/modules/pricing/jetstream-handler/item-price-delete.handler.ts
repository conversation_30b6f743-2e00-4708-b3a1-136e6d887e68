import { Injectable } from '@nestjs/common';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { ItemPrice } from '../entity/item-price.entity';
import { PricingService } from '../service/pricing.service';

@Injectable()
export class ItemPriceDeleteHandler {
  constructor(private readonly pricingService: PricingService) {}

  canHandle(payload: JetStreamPayload<ItemPrice>): boolean {
    return (
      payload.doctype === Doctypes.ITEM_PRICE &&
      payload.event === EventTypes.DELETED
    );
  }

  async handle(payload: JetStreamPayload<ItemPrice>): Promise<void> {
    await this.pricingService.deleteItemPrice(payload.data);
  }
}
