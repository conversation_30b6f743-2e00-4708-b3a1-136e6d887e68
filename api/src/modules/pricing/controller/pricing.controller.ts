import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { PricingService } from '../service/pricing.service';
import { PriceList } from '../entity/price-list.entity';
import { ItemPrice } from '../entity/item-price.entity';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('pricing')
@UseGuards(ApiKeyGuard)
export class PricingController {
  constructor(private readonly pricingService: PricingService) {}

  @Get('price-lists')
  async getPriceLists(): Promise<PriceList[]> {
    return this.pricingService.findAllPriceLists();
  }

  @Get('item-prices')
  async getItemPrices(
    @Query('price_list') priceList?: string,
  ): Promise<ItemPrice[]> {
    if (priceList) {
      return this.pricingService.findItemPricesByPriceList(priceList);
    }
    return this.pricingService.findAllItemPrices();
  }

  @Get('item-prices/:itemCode')
  async getItemPrice(
    @Param('itemCode') itemCode: string,
    @Query('price_list') priceList?: string,
  ): Promise<ItemPrice | null> {
    return this.pricingService.findItemPrice(itemCode, priceList);
  }
}
