import { Injectable } from '@nestjs/common';
import { DeleteResult, InsertResult, Repository } from 'typeorm';
import { PriceList } from '../entity/price-list.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { ItemPrice } from '../entity/item-price.entity';

@Injectable()
export class PricingService {
  constructor(
    @InjectRepository(PriceList)
    private readonly priceListRepository: Repository<PriceList>,
    @InjectRepository(ItemPrice)
    private readonly itemPriceRepository: Repository<ItemPrice>,
  ) {}

  async createOrUpdatePriceList(priceList: PriceList): Promise<InsertResult> {
    return this.priceListRepository.upsert(priceList, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deletePriceList(priceList: PriceList): Promise<DeleteResult> {
    return this.priceListRepository.delete({ name: priceList.name });
  }

  async createOrUpdateItemPrice(itemPrice: ItemPrice): Promise<InsertResult> {
    return this.itemPriceRepository.upsert(itemPrice, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteItemPrice(itemPrice: ItemPrice): Promise<DeleteResult> {
    return this.itemPriceRepository.delete({ name: itemPrice.name });
  }

  async findAllItemPrices(): Promise<ItemPrice[]> {
    return this.itemPriceRepository.find();
  }

  async findItemPrice(
    itemCode: string,
    priceList?: string,
  ): Promise<ItemPrice | null> {
    const whereCondition: { item_code: string; price_list?: string } = {
      item_code: itemCode,
    };
    if (priceList) {
      whereCondition.price_list = priceList;
    }
    const result = await this.itemPriceRepository.findOne({
      where: whereCondition,
    });
    return result ?? null;
  }

  async findItemPricesByPriceList(priceList: string): Promise<ItemPrice[]> {
    return this.itemPriceRepository.find({ where: { price_list: priceList } });
  }

  async findAllPriceLists(): Promise<PriceList[]> {
    return this.priceListRepository.find();
  }
}
