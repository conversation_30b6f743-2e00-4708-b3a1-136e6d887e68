import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';

/**
 * Guard that validates API key from Authorization header using Bearer format
 */
@Injectable()
export class ApiKeyGuard implements CanActivate {
  /**
   * Validates the API key from the Authorization header
   * @param context - The execution context
   * @returns true if API key is valid, throws UnauthorizedException otherwise
   */
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers['authorization'] as string;

    if (!authHeader) {
      throw new UnauthorizedException('Authorization header is required');
    }

    if (!authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException(
        'Invalid authorization format. Use Bearer token',
      );
    }

    const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    const validApiKey = process.env.API_KEY;
    if (!validApiKey) {
      throw new UnauthorizedException('API key not configured');
    }

    if (apiKey !== validApiKey) {
      throw new UnauthorizedException('Invalid API key');
    }

    return true;
  }
}
