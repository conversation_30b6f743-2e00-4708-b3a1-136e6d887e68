import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { Item } from '../entity/item.entity';

@Injectable()
export class ItemService {
  constructor(
    @InjectRepository(Item)
    private readonly itemRepository: Repository<Item>,
  ) {}

  async createOrUpdateItem(item: Item): Promise<InsertResult> {
    return this.itemRepository.upsert(item, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteItem(item: Item): Promise<void> {
    await this.itemRepository.delete(item.name);
  }

  async findItems(itemGroup?: string): Promise<Item[]> {
    if (itemGroup) {
      return this.itemRepository.find({ where: { item_group: itemGroup } });
    }
    return this.itemRepository.find();
  }
}
