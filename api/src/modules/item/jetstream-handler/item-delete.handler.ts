import { Injectable } from '@nestjs/common';
import { Doctypes } from '../../jetstream/enum/doctypes';
import { EventTypes } from '../../jetstream/enum/event-types';
import { JetstreamMessageHandler } from '../../jetstream/interface/jetstream-message-handler';
import { JetStreamPayload } from '../../jetstream/interface/jetstream-payload';
import { Item } from '../entity/item.entity';
import { ItemService } from '../service/item.service';

@Injectable()
export class ItemDeleteHandler implements JetstreamMessageHandler<Item> {
  constructor(private readonly itemService: ItemService) {}
  canHandle(message: JetStreamPayload<Item>): boolean {
    return (
      message.doctype === Doctypes.ITEM && message.event === EventTypes.DELETED
    );
  }

  async handle(message: JetStreamPayload<Item>): Promise<void> {
    await this.itemService.deleteItem(message.data);
  }
}
