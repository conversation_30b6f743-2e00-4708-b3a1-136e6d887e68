import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';

@Entity()
export class Item {
  @PrimaryColumn({ unique: true })
  name: string;

  @Column({ nullable: true })
  doctype?: string;

  @Column({ nullable: true })
  event?: string;

  @Column({ nullable: true })
  __run_link_triggers?: number;

  @Column({ nullable: true })
  __unsaved?: number;

  @Column({ nullable: true })
  allow_alternative_item?: number;

  @Column({ nullable: true })
  allow_negative_stock?: number;

  @Column({ nullable: true })
  asset_category?: string;

  @Column({ nullable: true })
  asset_naming_series?: string;

  @Column('jsonb', { nullable: true })
  attributes?: any[];

  @Column({ nullable: true })
  auto_create_assets?: number;

  @Column('jsonb', { nullable: true })
  barcodes?: any[];

  @Column({ nullable: true })
  batch_number_series?: string;

  @Column({ nullable: true })
  brand?: string;

  @Column({ nullable: true })
  country_of_origin?: string;

  @Column({ nullable: true })
  create_new_batch?: number;

  @Column({ nullable: true })
  creation?: string;

  @Column({ nullable: true })
  customer?: string;

  @Column({ nullable: true })
  customer_code?: string;

  @Column('jsonb', { nullable: true })
  customer_items?: any[];

  @Column({ nullable: true })
  customs_tariff_number?: string;

  @Column({ nullable: true })
  default_bom?: string;

  @Column({ nullable: true })
  default_item_manufacturer?: string;

  @Column({ nullable: true })
  default_manufacturer_part_no?: string;

  @Column({ nullable: true })
  default_material_request_type?: string;

  @Column({ nullable: true })
  delivered_by_supplier?: number;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  disabled?: number;

  @Column({ nullable: true })
  docstatus?: number;

  @Column({ nullable: true })
  enable_deferred_expense?: number;

  @Column({ nullable: true })
  enable_deferred_revenue?: number;

  @Column({ nullable: true })
  end_of_life?: string;

  @Column({ nullable: true })
  grant_commission?: number;

  @Column({ nullable: true })
  has_batch_no?: number;

  @Column({ nullable: true })
  has_expiry_date?: number;

  @Column({ nullable: true })
  has_serial_no?: number;

  @Column({ nullable: true })
  has_variants?: number;

  @Column({ nullable: true })
  idx?: number;

  @Column({ nullable: true })
  image?: string;

  @Column({ nullable: true })
  include_item_in_manufacturing?: number;

  @Column({ nullable: true })
  inspection_required_before_delivery?: number;

  @Column({ nullable: true })
  inspection_required_before_purchase?: number;

  @Column({ nullable: true })
  is_customer_provided_item?: number;

  @Column({ nullable: true })
  is_fixed_asset?: number;

  @Column({ nullable: true })
  is_grouped_asset?: number;

  @Column({ nullable: true })
  is_purchase_item?: number;

  @Column({ nullable: true })
  is_sales_item?: number;

  @Column({ nullable: true })
  is_stock_item?: number;

  @Column({ nullable: true })
  is_sub_contracted_item?: number;

  @Column({ nullable: true })
  item_code?: string;

  @Column('jsonb', { nullable: true })
  item_defaults?: any[];

  @Column({ nullable: true })
  item_group?: string;

  @Column({ nullable: true })
  item_name?: string;

  @Column({ nullable: true, type: 'float' })
  last_purchase_rate?: number;

  @Column({ nullable: true })
  lead_time_days?: number;

  @Column({ nullable: true, type: 'float' })
  max_discount?: number;

  @Column({ nullable: true, type: 'float' })
  min_order_qty?: number;

  @Column({ nullable: true })
  modified?: string;

  @Column({ nullable: true })
  modified_by?: string;

  @Column({ nullable: true })
  naming_series?: string;

  @Column({ nullable: true })
  no_of_months?: number;

  @Column({ nullable: true })
  no_of_months_exp?: number;

  @Column({ nullable: true, type: 'float' })
  opening_stock?: number;

  @Column({ nullable: true, type: 'float' })
  over_billing_allowance?: number;

  @Column({ nullable: true, type: 'float' })
  over_delivery_receipt_allowance?: number;

  @Column({ nullable: true })
  owner?: string;

  @Column({ nullable: true })
  purchase_uom?: string;

  @Column({ nullable: true })
  quality_inspection_template?: string;

  @Column('jsonb', { nullable: true })
  reorder_levels?: any[];

  @Column({ nullable: true })
  retain_sample?: number;

  @Column({ nullable: true, type: 'float' })
  safety_stock?: number;

  @Column({ nullable: true })
  sales_uom?: string;

  @Column({ nullable: true, type: 'float' })
  sample_quantity?: number;

  @Column({ nullable: true })
  serial_no_series?: string;

  @Column({ nullable: true })
  shelf_life_in_days?: number;

  @Column({ nullable: true, type: 'float' })
  standard_rate?: number;

  @Column({ nullable: true })
  stock_uom?: string;

  @Column('jsonb', { nullable: true })
  supplier_items?: any[];

  @Column('jsonb', { nullable: true })
  taxes?: any[];

  @Column({ nullable: true, type: 'float' })
  total_projected_qty?: number;

  @Column('jsonb', { nullable: true })
  uoms?: any[];

  @Column({ nullable: true })
  valuation_method?: string;

  @Column({ nullable: true, type: 'float' })
  valuation_rate?: number;

  @Column({ nullable: true })
  variant_based_on?: string;

  @Column({ nullable: true })
  variant_of?: string;

  @Column({ nullable: true })
  warranty_period?: string;

  @Column({ nullable: true, type: 'float' })
  weight_per_unit?: number;

  @Column({ nullable: true })
  weight_uom?: string;

  @Column('jsonb', { nullable: true })
  __onload?: any;
}
