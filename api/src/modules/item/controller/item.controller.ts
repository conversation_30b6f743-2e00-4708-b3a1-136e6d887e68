import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ItemService } from '../service/item.service';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('items')
@UseGuards(ApiKeyGuard)
export class ItemController {
  constructor(private readonly itemService: ItemService) {}

  @Get()
  async getItems(@Query('item_group') itemGroup?: string) {
    return this.itemService.findItems(itemGroup);
  }
}
