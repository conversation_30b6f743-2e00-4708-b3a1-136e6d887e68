FROM node:22-alpine AS dev

RUN apk --update add --no-cache --virtual curl vim

# Define variables
ENV APP_ROOT=/srv/app
WORKDIR ${APP_ROOT}
COPY [".docker/dev/entrypoint.sh", "${APP_ROOT}/.docker/dev/entrypoint.sh"]
RUN chmod +x .docker/dev/entrypoint.sh
ENTRYPOINT [".docker/dev/entrypoint.sh"]
EXPOSE 8080

# Prod stage
FROM node:22-alpine AS build-stage

# Install required dependencies
RUN apk add --no-cache gnupg

# Install Doppler CLI
# RUN wget -q https://cli.doppler.com/install.sh -O install.sh && \
#     sh install.sh && \
#     rm install.sh

# Add build argument for Doppler token
# ARG DOPPLER_TOKEN
# ENV DOPPLER_TOKEN=$DOPPLER_TOKEN

WORKDIR /app
COPY package*.json ./

# Clean install dependencies
RUN npm ci

COPY . .
# Run build with Doppler
# RUN doppler run -- npm run build
RUN npm run build

FROM nginx:alpine AS prod

COPY ./.docker/nginx/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/dist /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Default command to start Nginx when the container starts
CMD ["nginx", "-g", "daemon off;"]