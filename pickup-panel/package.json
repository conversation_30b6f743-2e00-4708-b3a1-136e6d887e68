{"name": "pickup-panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "@tailwindcss/vite": "^4.1.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^24.2.3", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.6", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.1.2", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/swiper": "^5.4.3", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}