import { io } from "socket.io-client";
import 'swiper/bundle';
import { Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import ErrorDisplay from '../components/ErrorDisplay';
import OrderCard from '../components/OrderCard';
import OrderHeader from '../components/OrderHeader';
import { Card, CardHeader, CardTitle } from '../components/ui/card';
import { useGetClients } from '../hooks/useGetClients';
import { useGetOrders } from '../hooks/useGetOrder';
import { Order } from "@/models/order.model";
import { useState, useEffect } from "react";

const OrderDisplay = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const initialOrders = useGetOrders();
  
  useEffect(() => {
    setOrders(initialOrders);
  }, [initialOrders]);
  
  const clients = useGetClients();
  const client = clients[0];

  useEffect(() => {
    const socket = io(`${import.meta.env.VITE_API_URL}/orders`);

    socket.on('order.created', (order: Order) => {
      console.log('order.created', order);
      setOrders(prevOrders => {
        const newOrders = [...prevOrders, order];
        return newOrders.sort((a, b) => 
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });
    });

    // Cleanup function
    return () => {
      socket.off('order.created');
      socket.disconnect();
    };
  }, []); // Empty dependency array since we don't need to recreate the socket on re-renders

  if (!client) {
    return <ErrorDisplay message="No restaurant found. Please check your configuration." />;
  }

  // Filter out picked up orders and separate the rest
  const preparingOrders = orders.filter(order => order.status === 'in_progress');
  const readyToPickupOrders = orders.filter(order => order.status === 'ready_for_pickup');

  return (
    <div className="min-h-screen bg-background overflow-hidden fixed inset-0">
      <div className="max-w-10xl mx-auto px-4 h-full">
        <OrderHeader client={client} />

        <div className="grid grid-cols-12 gap-4">
          {/* Preparing Orders Column */}
          <div className="col-span-5">
            <Card className="mb-4">
              <CardHeader className="p-4">
                <CardTitle className="text-2xl text-blue-800 text-center">Preparing</CardTitle>
              </CardHeader>
            </Card>
            <div className="h-[70vh] overflow-hidden">
              <Swiper
                direction="vertical"
                slidesPerView="auto"
                spaceBetween={12}
                speed={400}
                autoplay={{
                  delay: 100,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true,
                  waitForTransition: true,
                }}
                modules={[Autoplay]}
                className="h-full swiper-no-swiping"
                mousewheel={true}
                noSwiping={true}
                allowTouchMove={false}
              >
                {preparingOrders.map((order) => (
                  <SwiperSlide key={order._id} className="h-auto">
                    <OrderCard order={order} />
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>

          {/* Done Orders Column */}
          <div className="col-span-7">
            <Card className="mb-4">
              <CardHeader className="p-4">
                <CardTitle className="text-2xl text-green-800 text-center">Ready for Pickup</CardTitle>
              </CardHeader>
            </Card>
            <div className="h-[70vh] overflow-hidden">
              <Swiper
                direction="vertical"
                slidesPerView="auto"
                spaceBetween={12}
                speed={800}
                loop={true}
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: false,
                  waitForTransition: false,
                }}
                modules={[Autoplay]}
                className="h-full swiper-no-swiping"
                mousewheel={true}
                noSwiping={true}
                allowTouchMove={false}
              >
                {readyToPickupOrders.map((order) => (
                  <SwiperSlide key={order._id} className="h-auto">
                    <OrderCard order={order} />
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDisplay; 