import { useEffect, useState } from "react";
import { Order } from "../models/order.model";
import axios from "axios";

export const useGetOrders = () => {
    const [orders, setOrders] = useState<Order[]>([])

    useEffect(() => {
        axios.get(`${import.meta.env.VITE_API_URL}/orders`)
            .then(res => {
                const sortedOrders = res.data.sort((a: Order, b: Order) => 
                    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
                );
                setOrders(sortedOrders);
            })
    }, [])

    return orders;
}