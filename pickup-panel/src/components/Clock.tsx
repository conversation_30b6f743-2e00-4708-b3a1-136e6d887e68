import { useEffect, useState } from 'react';

const Clock = () => {
  const [currentTime, setCurrentTime] = useState<string>(new Date().toLocaleTimeString());

  useEffect(() => {
    // Update time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <p className="text-2xl text-muted-foreground">{currentTime}</p>
  );
};

export default Clock; 