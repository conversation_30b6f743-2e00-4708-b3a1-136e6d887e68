import { Order } from '../models/order.model';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';

interface OrderCardProps {
  order: Order;
}

const OrderCard = ({ order }: OrderCardProps) => {
  return (
    <Card className="hover:shadow-xl transition-shadow duration-300">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-3xl">Order #{order._id}</CardTitle>
            {order.tableNumber && (
              <p className="text-xl text-muted-foreground">Table {order.tableNumber}</p>
            )}
          </div>
          {order.estimatedTime && order.status === 'in_progress' && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-lg">
              {order.estimatedTime}
            </Badge>
          )}
        </div>
      </CardHeader>
    </Card>
  );
};

export default OrderCard; 