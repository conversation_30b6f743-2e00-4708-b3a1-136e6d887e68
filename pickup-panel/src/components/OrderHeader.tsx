import { Client } from '../models/client.model';
import Clock from './Clock';
import <PERSON><PERSON><PERSON><PERSON> from './MockLogo';

interface OrderHeaderProps {
  client: Client;
}

const OrderHeader = ({ client }: OrderHeaderProps) => {
  return (
    <div className="flex items-center justify-between mb-12 p-4">
      <div className="flex items-center gap-4">
        <MockLogo />
        <h1 className="text-4xl font-bold">{client.name}</h1>
        <Clock />
      </div>

      <h2 className="text-3xl font-semibold text-primary">Order Status Display</h2>
    </div>
  );
};

export default OrderHeader; 