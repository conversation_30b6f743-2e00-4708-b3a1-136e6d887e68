services:

  saba-pickup-panel:
    container_name: saba-pickup-panel
    networks:
      - saba-network
    build:
      context: ./pickup-panel
      dockerfile: Dockerfile
      target: dev
    volumes:
      - ./pickup-panel:/srv/app
    ports:
      - "8081:8081"
    depends_on:
      - saba-api

  saba-pos-frontend:
    container_name: saba-pos-frontend
    networks:
      - saba-network
    build:
      context: ./pos-frontend
      dockerfile: Dockerfile
      target: dev
    volumes:
      - ./pos-frontend:/srv/app
    ports:
      - "8082:8082"
    depends_on:
      - saba-api

  saba-kitchen-panel:
    container_name: saba-kitchen-panel
    networks:
      - saba-network
    build:
      context: ./kitchen-panel
      dockerfile: Dockerfile
      target: dev
    volumes:
      - ./kitchen-panel:/srv/app
    ports:
      - "8083:8083"
    depends_on:
      - saba-api

  saba-api:
    container_name: saba-api
    build:
      context: ./api
      dockerfile: Dockerfile
      target: dev
    restart: unless-stopped
    volumes:
      - ./api:/srv/app
    ports:
      - "3000:3000"
    environment:
      DB_HOST: saba-postgres
      DB_PORT: 5432
      DB_USERNAME: user
      DB_PASSWORD: password
      DB_NAME: saba
      NODE_ENV: development
      NATS_URL: nats://saba-frappe-nats:4222
      NATS_SUBJECT: saba.entity_changes
      NATS_DURABLE_CONSUMER_NAME: local-api-consumer
      NATS_DURABLE_CONSUMER_DELIVER_TO: local-api-deliver
      API_KEY: 5Yf!mBcd5ysd3Eor
    networks:
      - saba-network
      - saba-frappe-network
    depends_on:
      - saba-postgres

  saba-postgres:
    container_name: saba-postgres
    image: postgres:16-alpine
    networks:
      - saba-network
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: saba
      TZ: "Europe/Berlin"
    volumes:
      - saba-postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d saba"]
      interval: 10s
      timeout: 5s
      retries: 5

  saba-pgadmin:
    container_name: saba-pgadmin
    image: dpage/pgadmin4:latest
    networks:
      - saba-network
    ports:
      - "8084:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - saba-pgadmin-data:/var/lib/pgadmin
    depends_on:
      - saba-postgres




networks:
  saba-network:
  saba-frappe-network:
    external: true

volumes:
  saba-postgres-data:
  saba-pgadmin-data:
  saba-erpnext-mariadb-data: