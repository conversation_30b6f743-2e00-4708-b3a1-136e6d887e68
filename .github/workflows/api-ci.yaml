name: API CI Workflow
on:
  workflow_call:

concurrency:
  group: api-ci-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  install-deps:
    runs-on: saba-technology-scale-set
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: 💾 Cache node_modules
        uses: actions/cache@v4
        with:
          path: ./api/node_modules
          key: ${{ runner.os }}-api-node-modules-${{ hashFiles('api/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-api-node-modules-

      - name: 📥 Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: ./api/package-lock.json

      - name: 🧪 Install dependencies for API
        working-directory: ./api
        run: npm ci

  lint-check:
    needs: install-deps
    runs-on: saba-technology-scale-set
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: 📥 Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: ./api/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./api/node_modules
          key: ${{ runner.os }}-api-node-modules-${{ hashFiles('api/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-api-node-modules-

      - name: 🧹 Run lint check
        working-directory: ./api
        run: npm run lint:check

  unit-tests:
    needs: install-deps
    runs-on: saba-technology-scale-set
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: 📥 Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: ./api/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./api/node_modules
          key: ${{ runner.os }}-api-node-modules-${{ hashFiles('api/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-api-node-modules-

      - name: 🧪 Run unit tests with coverage
        working-directory: ./api
        run: npm run test:unit:cov

  e2e-tests:
    needs: install-deps
    runs-on: saba-technology-scale-set
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: 📥 Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: ./api/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./api/node_modules
          key: ${{ runner.os }}-api-node-modules-${{ hashFiles('api/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-api-node-modules-

      - name: 🧪 Run e2e tests with coverage
        working-directory: ./api
        run: npm run test:e2e:cov

  build-docker-image:
    if: github.event_name == 'pull_request'
    needs: install-deps
    runs-on: saba-technology-scale-set
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: 📥 Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: ./api/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./api/node_modules
          key: ${{ runner.os }}-api-node-modules-${{ hashFiles('api/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-api-node-modules-

      - name: 🏷️ Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api
          tags: |
            type=raw,value=pr-${{ github.event.pull_request.number }}

      - name: 🐳 Build Docker image api
        uses: docker/build-push-action@v6.15.0
        with:
          context: ./api
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: prod 