name: CI/CD Workflow
on:
  push:
    branches:
      - main
      - production
  workflow_dispatch:

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

permissions:
  contents: read
  packages: write

jobs:
  api-ci:
    uses: ./.github/workflows/api-ci.yaml
    secrets: inherit

  pos-frontend-ci:
    uses: ./.github/workflows/pos-frontend-ci.yaml
    secrets: inherit

  build-api-docker-image:
    needs: [api-ci, pos-frontend-ci]
    runs-on: saba-technology-scale-set
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        
      - name: 🏷️ Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api
          tags: |
            type=raw,value=latest
            type=sha,format=short

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: 🐳 Build Docker image api
        uses: docker/build-push-action@v6.15.0
        with:
          context: ./api
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: prod 
          cache-from: type=gha
          cache-to: type=gha,mode=max

  build-pos-frontend-docker-image:
    needs: [api-ci, pos-frontend-ci]
    runs-on: saba-technology-scale-set
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        
      - name: 🏷️ Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-pos-frontend
          tags: |
            type=raw,value=latest
            type=sha,format=short
            
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: 🐳 Build Docker image pos-frontend
        uses: docker/build-push-action@v6.15.0
        with:
          context: ./pos-frontend
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: prod 
          cache-from: type=gha
          cache-to: type=gha,mode=max