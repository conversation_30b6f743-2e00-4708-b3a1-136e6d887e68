name: Pickup Panel CI Workflow
on:
  workflow_call:

concurrency:
  group: pickup-panel-ci-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  cache-repo:
    runs-on: saba-technology-scale-set
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 💾 Cache repository
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/actions/checkout
            ${{ github.workspace }}
          key: ${{ runner.os }}-repo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-repo-

  pickup-panel:
    needs: cache-repo
    runs-on: saba-technology-scale-set
    steps:
      - name: 📥 Restore repository from cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/actions/checkout
            ${{ github.workspace }}
          key: ${{ runner.os }}-repo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-repo-

      - name: 🏷️ Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=pr-${{ github.event.pull_request.number }}

      - name: 🐳 Build Docker image pickup-panel
        uses: docker/build-push-action@v6.15.0
        with:
          context: ./pickup-panel
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: prod 