import { renderHook } from '@testing-library/react';
import { usePriceFormat } from '../../src/hooks/usePriceFormat';
import { ItemPrice } from '../../src/model/item-price.model';

describe('usePriceFormat', () => {
  let hook: ReturnType<typeof usePriceFormat>;

  beforeEach(() => {
    const { result } = renderHook(() => usePriceFormat());
    hook = result.current;
  });

  describe('formatPrice', () => {
    it('should format price with currency by default', () => {
      const result = hook.formatPrice(25.5, 'USD');
      expect(result).toBe('25.50 USD');
    });

    it('should format price without currency when showCurrency is false', () => {
      const result = hook.formatPrice(25.5, 'USD', false);
      expect(result).toBe('25.50');
    });

    it('should respect custom decimal places', () => {
      const result = hook.formatPrice(25.123, 'EUR', true, 3);
      expect(result).toBe('25.123 EUR');
    });

    it('should handle zero amounts', () => {
      const result = hook.formatPrice(0, 'USD');
      expect(result).toBe('0.00 USD');
    });

    it('should handle empty currency', () => {
      const result = hook.formatPrice(25.5, '');
      expect(result).toBe('25.50');
    });

    it('should handle negative amounts', () => {
      const result = hook.formatPrice(-10.99, 'USD');
      expect(result).toBe('-10.99 USD');
    });

    it('should use default decimals when not specified', () => {
      const result = hook.formatPrice(25, 'USD');
      expect(result).toBe('25.00 USD');
    });
  });

  describe('formatItemPrice', () => {
    const mockPriceObj: ItemPrice = {
      name: 'Test Price',
      item_code: 'TEST001',
      price_list: 'Standard',
      price_list_rate: 15.99,
      currency: 'USD'
    };

    it('should format item price with currency', () => {
      const result = hook.formatItemPrice(mockPriceObj);
      expect(result).toBe('15.99 USD');
    });

    it('should format item price without currency', () => {
      const result = hook.formatItemPrice(mockPriceObj, false);
      expect(result).toBe('15.99');
    });

    it('should handle null price object', () => {
      const result = hook.formatItemPrice(null);
      expect(result).toBe('0.00');
    });

    it('should handle undefined price object', () => {
      const result = hook.formatItemPrice(undefined);
      expect(result).toBe('0.00');
    });

    it('should handle price object without currency', () => {
      const priceObjNoCurrency = { ...mockPriceObj, currency: undefined };
      const result = hook.formatItemPrice(priceObjNoCurrency);
      expect(result).toBe('15.99');
    });

    it('should handle price object without price_list_rate', () => {
      const priceObjNoRate = { ...mockPriceObj, price_list_rate: undefined };
      const result = hook.formatItemPrice(priceObjNoRate);
      expect(result).toBe('0.00 USD');
    });

    it('should respect custom decimal places for item prices', () => {
      const result = hook.formatItemPrice(mockPriceObj, true, 3);
      expect(result).toBe('15.990 USD');
    });
  });

  describe('edge cases', () => {
    it('should handle very large numbers', () => {
      const result = hook.formatPrice(999999.99, 'USD');
      expect(result).toBe('999999.99 USD');
    });

    it('should handle very small numbers', () => {
      const result = hook.formatPrice(0.01, 'USD');
      expect(result).toBe('0.01 USD');
    });

    it('should handle numbers with many decimal places', () => {
      const result = hook.formatPrice(25.123456789, 'USD', true, 5);
      expect(result).toBe('25.12346 USD');
    });
  });
});