# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Start development server**: `npm run dev` (runs on port 8082)
- **Build for production**: `npm run build` (includes TypeScript compilation with `tsc -b`)
- **Lint code**: `npm run lint`
- **Preview production build**: `npm run preview`
- **Run tests**: `npm test`
- **Run tests in watch mode**: `npm run test:watch`
- **Run tests with coverage**: `npm run test:coverage`

## Architecture Overview

This is a React + TypeScript + Vite frontend application for a POS (Point of Sale) system.

### Tech Stack
- **React 19** with TypeScript
- **Vite** for build tooling and dev server
- **TailwindCSS 4.1** for styling with Radix UI components
- **React Query (@tanstack/react-query)** for server state management
- **React Router** for routing
- **i18next** for internationalization (supports EN, AR, DE, FR)
- **Axios** for HTTP requests
- **Socket.io** client for real-time communication
- **Jest + React Testing Library** for unit testing

### Project Structure
- **Routes**: Defined in `src/routes.tsx` with Layout wrapper
- **Pages**: `Home` and `Settings` pages in `src/pages/`
- **Components**: Reusable components in `src/components/` with UI primitives in `src/components/ui/`
- **Hooks**: 
  - Data fetching hooks in `src/hooks/` (item-group, item-price, item, pos-profile)
  - Utility hooks in `src/hooks/` (usePriceFormat)
- **Models**: TypeScript interfaces in `src/model/` for API data structures
- **i18n**: Translation files in `src/i18n/` for multi-language support
- **Tests**: Unit tests in `test/` directory mirroring `src/` structure

### Key Patterns
- **Data Fetching**: Uses React Query hooks for server state (see `src/hooks/`)
- **Styling**: TailwindCSS classes exclusively, no CSS files except `index.css`
- **Components**: Functional components with TypeScript interfaces
- **State Management**: React Query for server state, React context for theme
- **Price Formatting**: ALWAYS use `usePriceFormat` hook for all price displays. Never use manual `.toFixed(2)` formatting.

### Development Guidelines (from .cursorrules)
- Use early returns for readability
- Always use TailwindCSS classes for styling
- Use descriptive variable names with "handle" prefix for event functions
- Implement accessibility features (tabindex, aria-label, etc.)
- Use `const` instead of `function` declarations
- All text must be translated using i18n files in `src/i18n/`
- Environment variables stored in `.env`

### API Integration
- Base API calls use axios without explicit base URL configuration
- Custom hooks follow pattern: `use[EntityName]` for queries
- Models define TypeScript interfaces for API responses

### Build Configuration
- Vite with React SWC plugin for fast refresh
- Path alias `@` points to `./src`
- Development server on port 8082 with host exposure enabled