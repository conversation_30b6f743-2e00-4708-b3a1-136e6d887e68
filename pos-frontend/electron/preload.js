const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // Hardware integration placeholders
  printer: {
    print: (data) => ipcRenderer.invoke('printer-print', data),
    getStatus: () => ipcRenderer.invoke('printer-status')
  },
  paymentTerminal: {
    processPayment: (amount) => ipcRenderer.invoke('payment-process', amount),
    getStatus: () => ipcRenderer.invoke('payment-status')
  },
  // System utilities
  platform: process.platform,
  version: process.env.npm_package_version
});

// Log successful preload
console.log('[Preload] Electron API bridge initialized successfully');