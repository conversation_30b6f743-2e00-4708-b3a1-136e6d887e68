import { app, BrowserWindow, ipcMain } from 'electron';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { PrinterService } from './hardware/printer.js';

// Mock Payment Terminal Service
class MockPaymentTerminalService {
  async processPayment({ amount, simulateSuccess }) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (simulateSuccess === true) {
          resolve({ success: true, transactionId: 'mock-txn-123', amount, cardLast4: '1234', cardType: 'Visa', approvalCode: 'ABCDEF' });
        } else if (simulateSuccess === false) {
          resolve({ success: false, transactionId: null, amount, cardLast4: '', cardType: '', approvalCode: '', error: 'Mock payment failed: Simulated failure.' });
        } else if (amount > 0) {
          resolve({ success: true, transactionId: 'mock-txn-123', amount, cardLast4: '1234', cardType: 'Visa', approvalCode: 'ABCDEF' });
        } else {
          resolve({ success: false, transactionId: null, amount, cardLast4: '', cardType: '', approvalCode: '', error: 'Mock payment failed: Invalid amount.' });
        }
      }, 1500); // Simulate network delay
    });
  }

  async getStatus() {
    return { connected: true, ready: true, cardPresent: false };
  }
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const isDev = process.env.NODE_ENV === 'development';

// Initialize hardware services
const printerService = new PrinterService();
const paymentService = new MockPaymentTerminalService(); // Use the mock service

function createMainWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true
    },
    show: false,
    title: process.env.VITE_APP_MODE === 'cashier' ? 'Saba POS - Cashier' : 'Saba POS - Self Ordering'
  });

  const startUrl = isDev 
    ? 'http://localhost:8082' 
    : `file://${path.join(__dirname, '../dist/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  mainWindow.on('closed', () => {
    app.quit();
  });

  return mainWindow;
}

// Setup IPC handlers
function setupIpcHandlers() {
  // Printer IPC handlers
  ipcMain.handle('printer-print', async (_event, data) => {
    try {
      if (!data) {
        throw new Error('Print data is required');
      }
      return await printerService.print(data);
    } catch (error) {
      console.error('[IPC] Printer print error:', error);
      return { success: false, message: error.message, jobId: null };
    }
  });
  
  ipcMain.handle('printer-status', async () => {
    try {
      return await printerService.getStatus();
    } catch (error) {
      console.error('[IPC] Printer status error:', error);
      return { connected: false, ready: false, paper: 'out', error: error.message };
    }
  });

  // Payment terminal IPC handlers
  ipcMain.handle('mock-payment', async (_event, { amount, simulateSuccess }) => {
    try {
      if (!amount || amount <= 0) {
        throw new Error('Valid payment amount is required');
      }
      return await paymentService.processPayment({ amount });
    } catch (error) {
      console.error('[IPC] Payment process error:', error);
      return { success: false, transactionId: null, amount, cardLast4: '', cardType: '', approvalCode: '', error: error.message };
    }
  });
  
  ipcMain.handle('payment-status', async () => {
    try {
      return await paymentService.getStatus();
    } catch (error) {
      console.error('[IPC] Payment status error:', error);
      return { connected: false, ready: false, cardPresent: false, error: error.message };
    }
  });
}

app.whenReady().then(() => {
  setupIpcHandlers();
  createMainWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});