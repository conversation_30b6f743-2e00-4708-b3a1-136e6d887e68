
// src/lib/PaymentService.ts

interface PaymentResult {
  success: boolean;
  message: string;
}

interface PaymentService {
  startPayment(amount: number, simulateSuccess?: boolean): Promise<PaymentResult>;
}

declare global {
  interface Window {
    electron?: {
      invoke: (channel: string, ...args: unknown[]) => Promise<unknown>;
    };
  }
}

class ElectronPaymentService implements PaymentService {
  async startPayment(amount: number, simulateSuccess?: boolean): Promise<PaymentResult> {
    if (!window.electron) {
      return { success: false, message: "Electron IPC bridge not found." };
    }
    try {
      const result = await window.electron.invoke('mock-payment', { amount, simulateSuccess }) as PaymentResult;
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Electron payment failed.";
      return { success: false, message: errorMessage };
    }
  }
}

class BrowserPaymentService implements PaymentService {
  async startPayment(amount: number, simulateSuccess?: boolean): Promise<PaymentResult> {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (simulateSuccess === true) {
          resolve({ success: true, message: "Browser mock payment successful." });
        } else if (simulateSuccess === false) {
          resolve({ success: false, message: "Browser mock payment failed: Simulated failure." });
        } else if (amount > 0) { // Default to success for positive amount if simulateSuccess is undefined
          resolve({ success: true, message: "Browser mock payment successful." });
        } else {
          resolve({ success: false, message: "Browser mock payment failed: Invalid amount." });
        }
      }, 1500); // Simulate network delay
    });
  }
}

const paymentService: PaymentService = window.electron
  ? new ElectronPaymentService()
  : new BrowserPaymentService();

export default paymentService;
