/* eslint-disable @typescript-eslint/no-explicit-any */
export interface PosProfileUser {
  __unsaved?: number;
  creation?: string;
  default?: number;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  modified?: string;
  modified_by?: string;
  name?: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
  user?: string | null;
}

export interface PosCustomerGroup {
  __unsaved?: number;
  creation?: string;
  customer_group?: string;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  modified?: string;
  modified_by?: string;
  name?: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
}

export interface PosPaymentMethod {
  __unsaved?: number;
  allow_in_returns?: number;
  creation?: string;
  default?: number;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  mode_of_payment?: string;
  modified?: string;
  modified_by?: string;
  name?: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
}

export interface PosProfile {
  name: string;
  __unsaved?: number;
  account_for_change_amount?: string;
  allow_discount_change?: number;
  allow_rate_change?: number;
  applicable_for_users?: PosProfileUser[];
  apply_discount_on?: string;
  auto_add_item_to_cart?: number;
  campaign?: string;
  company?: string;
  company_address?: string;
  cost_center?: string;
  country?: string;
  creation?: string;
  currency?: string;
  customer?: string;
  customer_groups?: PosCustomerGroup[];
  disable_grand_total_to_default_mop?: number;
  disable_rounded_total?: number;
  disabled?: number;
  docstatus?: number;
  doctype?: string;
  expense_account?: string;
  hide_images?: number;
  hide_unavailable_items?: number;
  idx?: number;
  ignore_pricing_rule?: number;
  income_account?: string;
  item_groups?: any[];
  letter_head?: string;
  modified?: string;
  modified_by?: string;
  owner?: string;
  payments?: PosPaymentMethod[];
  print_format?: string;
  print_receipt_on_order_complete?: number;
  select_print_heading?: string;
  selling_price_list?: string;
  tax_category?: string;
  taxes_and_charges?: string;
  tc_name?: string;
  update_stock?: number;
  validate_stock_on_save?: number;
  warehouse?: string;
  write_off_account?: string;
  write_off_cost_center?: string;
  write_off_limit?: number;
}