/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ItemGroup {
  name: string;
  description?: string;
  __unsaved?: number;
  creation?: string;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  image?: string;
  is_group?: number;
  item_group_defaults?: any[];
  item_group_name?: string;
  lft?: number;
  modified?: string;
  modified_by?: string;
  old_parent?: string;
  owner?: string;
  parent_item_group?: string;
  rgt?: number;
  taxes?: any[];
}

export const RootItemGroup = "All Item Groups";
