/* eslint-disable @typescript-eslint/no-explicit-any */
export interface Item {
  name: string;
  doctype?: string;
  event?: string;
  __run_link_triggers?: number;
  __unsaved?: number;
  allow_alternative_item?: number;
  allow_negative_stock?: number;
  asset_category?: string;
  asset_naming_series?: string;
  attributes?: any[];
  auto_create_assets?: number;
  barcodes?: any[];
  batch_number_series?: string;
  brand?: string;
  country_of_origin?: string;
  create_new_batch?: number;
  creation?: string;
  customer?: string;
  customer_code?: string;
  customer_items?: any[];
  customs_tariff_number?: string;
  default_bom?: string;
  default_item_manufacturer?: string;
  default_manufacturer_part_no?: string;
  default_material_request_type?: string;
  delivered_by_supplier?: number;
  description?: string;
  disabled?: number;
  docstatus?: number;
  enable_deferred_expense?: number;
  enable_deferred_revenue?: number;
  end_of_life?: string;
  grant_commission?: number;
  has_batch_no?: number;
  has_expiry_date?: number;
  has_serial_no?: number;
  has_variants?: number;
  idx?: number;
  image?: string;
  include_item_in_manufacturing?: number;
  inspection_required_before_delivery?: number;
  inspection_required_before_purchase?: number;
  is_customer_provided_item?: number;
  is_fixed_asset?: number;
  is_grouped_asset?: number;
  is_purchase_item?: number;
  is_sales_item?: number;
  is_stock_item?: number;
  is_sub_contracted_item?: number;
  item_code?: string;
  item_defaults?: any[];
  item_group?: string;
  item_name?: string;
  last_purchase_rate?: number;
  lead_time_days?: number;
  max_discount?: number;
  min_order_qty?: number;
  modified?: string;
  modified_by?: string;
  naming_series?: string;
  no_of_months?: number;
  no_of_months_exp?: number;
  opening_stock?: number;
  over_billing_allowance?: number;
  over_delivery_receipt_allowance?: number;
  owner?: string;
  purchase_uom?: string;
  quality_inspection_template?: string;
  reorder_levels?: any[];
  retain_sample?: number;
  safety_stock?: number;
  sales_uom?: string;
  sample_quantity?: number;
  serial_no_series?: string;
  shelf_life_in_days?: number;
  standard_rate?: number;
  stock_uom?: string;
  supplier_items?: any[];
  taxes?: any[];
  total_projected_qty?: number;
  uoms?: any[];
  valuation_method?: string;
  valuation_rate?: number;
  variant_based_on?: string;
  variant_of?: string;
  warranty_period?: string;
  weight_per_unit?: number;
  weight_uom?: string;
  __onload?: any;
}
