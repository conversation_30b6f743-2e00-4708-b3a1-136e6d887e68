export interface ItemPrice {
  name: string;
  __run_link_triggers?: number;
  __unsaved?: number;
  batch_no?: string;
  brand?: string;
  buying?: number;
  creation?: string;
  currency?: string;
  customer?: string;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  item_code?: string;
  item_description?: string;
  item_name?: string;
  lead_time_days?: number;
  modified?: string;
  modified_by?: string;
  note?: string;
  owner?: string;
  packing_unit?: number;
  price_list?: string;
  price_list_rate?: number;
  reference?: string;
  selling?: number;
  supplier?: string;
  uom?: string;
  valid_from?: string;
  valid_upto?: string;
} 