export interface PriceListCountry {
  __unsaved?: number;
  country: string;
  creation?: string;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  modified?: string;
  modified_by?: string;
  name: string;
  owner?: string;
  parent?: string;
  parentfield?: string;
  parenttype?: string;
}

export interface PriceList {
  name: string;
  creation?: string;
  docstatus?: number;
  doctype?: string;
  idx?: number;
  modified?: string;
  modifiedBy?: string;
  owner?: string;
  priceListName?: string;
  currency?: string;
  enabled?: number;
  priceNotUomDependent?: number;
  buying?: number;
  selling?: number;
  isUnsaved?: number;
  countries?: PriceListCountry[];
} 