export const translationsFR = {
  header: "header",
  footer: "footer",
  language: {
    english: "Ang<PERSON><PERSON>",
    german: "Allemand",
    french: "Français",
    arabic: "Arabe",
    label: "Langues disponibles",
  },
  theme: {
    label: "Thème",
    light: "Clair",
    dark: "Sombre",
  },
  api: {
    health: "Santé de l'API",
    healthy: "Disponible",
    unreachable: "Indisponible",
    checking: "Vérification...",
    healthyDesc: "L'API est accessible et fonctionne comme prévu.",
    unreachableDesc: "L'API est actuellement indisponible.",
    checkingDesc: "Vérification de l'état de santé de l'API...",
  },
  welcome: {
    title: "Bienvenue sur Saba POS",
    subtitle: "Touchez l'écran pour commencer votre commande",
    startOrder: "Commencer la commande",
  },
  common: {
    loading: "Chargement...",
    back: "Retour",
  },
  menu: {
    title: "Menu",
    categories: "Catégories",
    addToCart: "Ajouter au panier",
    noItems: "Aucun article trouvé",
    selectCategory: "Sélectionnez une catégorie",
    priceNotAvailable: "Prix non disponible",
    loadingCategories: "Chargement des catégories...",
    loadingItems: "Chargement des articles...",
  },
  cart: {
    title: "Panier",
    empty: "Votre panier est vide",
    total: "Total",
    each: "chacun",
    remove: "Supprimer l'article",
    item: "article",
    items: "articles",
    viewCart: "Voir le panier",
    quantity: "Quantité",
    increaseQuantity: "Augmenter la quantité",
    decreaseQuantity: "Diminuer la quantité",
  },
  order: {
    summary: "Résumé de la commande",
    subtotal: "Sous-total",
    tax: "Taxe",
    total: "Total",
    proceedToPayment: "Procéder au paiement",
    continueShopping: "Continuer les achats",
    backToMenu: "Retour au menu",
    emptyCartPaymentDisabled: "Ajoutez des articles au panier avant de procéder au paiement",
  },
  payment: {
    title: "Paiement",
    amountDue: "Montant dû",
    orderSummary: "Résumé de la commande",
    selectAction: "Sélectionner une action de paiement",
    simulateSuccess: "Traiter le paiement (Succès)",
    simulateFailure: "Simuler un échec de paiement",
    processing: "Traitement du paiement...",
    pleaseWait: "Veuillez patienter pendant que nous traitons votre paiement",
    approved: "Paiement approuvé",
    declined: "Paiement refusé",
    error: "Erreur de paiement",
    ready: "Prêt pour le paiement",
    retry: "Recommencer le paiement",
    backToCart: "Retour au panier",
    redirecting: "Redirection vers la confirmation...",
  },
  confirmation: {
    title: "Confirmation de commande",
    success: "Paiement réussi !",
    message: "Votre commande a été confirmée et le paiement traité.",
    orderDetails: "Détails de la commande",
    orderNumber: "Numéro de commande",
    orderTime: "Heure de commande",
    paymentMethod: "Méthode de paiement",
    status: "Statut",
    card: "Carte de crédit",
    paid: "Payé",
    whatNext: "Que souhaitez-vous faire ?",
    printReceipt: "Imprimer le reçu",
    newOrder: "Commencer une nouvelle commande",
    autoRedirect: "Vous serez redirigé vers la page d'accueil dans quelques secondes.",
    thankYou: "Merci !",
    enjoyMeal: "Nous espérons que vous apprécierez votre repas.",
  },
};