export const translationsDE = {
  header: "header",
  footer: "footer",
  language: {
    english: "Englisch",
    german: "Deutsch",
    french: "Französisch",
    arabic: "Arabisch",
    label: "Verfügbare Sprachen",
  },
  theme: {
    label: "Thema",
    light: "Hell",
    dark: "Dunkel",
  },
  api: {
    health: "API-Gesundheit",
    healthy: "Erreichbar",
    unreachable: "Nicht erreichbar",
    checking: "Prüfe...",
    healthyDesc: "Die API ist erreichbar und funktioniert wie erwartet.",
    unreachableDesc: "Die API ist derzeit nicht erreichbar.",
    checkingDesc: "API-Gesundheit wird überprüft...",
  },
  welcome: {
    title: "Willkommen bei Saba POS",
    subtitle: "Berühren Sie den Bildschirm, um Ihre Bestellung zu starten",
    startOrder: "Bestellung starten",
  },
  common: {
    loading: "Laden...",
    back: "Zurück",
  },
  menu: {
    title: "Menü",
    categories: "Kategorien",
    addToCart: "In den Warenkorb",
    noItems: "Keine Artikel gefunden",
    selectCategory: "Wählen Sie eine Kategorie aus",
    priceNotAvailable: "Preis nicht verfügbar",
    loadingCategories: "Kategorien werden geladen...",
    loadingItems: "Artikel werden geladen...",
  },
  cart: {
    title: "Warenkorb",
    empty: "Ihr Warenkorb ist leer",
    total: "Gesamt",
    each: "pro Stück",
    remove: "Artikel entfernen",
    item: "Artikel",
    items: "Artikel",
    viewCart: "Warenkorb anzeigen",
    quantity: "Menge",
    increaseQuantity: "Menge erhöhen",
    decreaseQuantity: "Menge verringern",
  },
  order: {
    summary: "Bestellübersicht",
    subtotal: "Zwischensumme",
    tax: "Steuer",
    total: "Gesamt",
    proceedToPayment: "Zur Zahlung",
    continueShopping: "Weiter einkaufen",
    backToMenu: "Zurück zum Menü",
    emptyCartPaymentDisabled: "Fügen Sie Artikel zum Warenkorb hinzu, bevor Sie zur Zahlung fortfahren",
  },
  payment: {
    title: "Zahlung",
    amountDue: "Zu zahlender Betrag",
    orderSummary: "Bestellübersicht",
    selectAction: "Zahlungsaktion wählen",
    simulateSuccess: "Zahlung verarbeiten (Erfolg)",
    simulateFailure: "Zahlungsfehlschlag simulieren",
    processing: "Zahlung wird verarbeitet...",
    pleaseWait: "Bitte warten Sie, während wir Ihre Zahlung verarbeiten",
    approved: "Zahlung genehmigt",
    declined: "Zahlung abgelehnt",
    error: "Zahlungsfehler",
    ready: "Bereit für Zahlung",
    retry: "Zahlung wiederholen",
    backToCart: "Zurück zum Warenkorb",
    redirecting: "Weiterleitung zur Bestätigung...",
  },
  confirmation: {
    title: "Bestellbestätigung",
    success: "Zahlung erfolgreich!",
    message: "Ihre Bestellung wurde bestätigt und die Zahlung verarbeitet.",
    orderDetails: "Bestelldetails",
    orderNumber: "Bestellnummer",
    orderTime: "Bestellzeit",
    paymentMethod: "Zahlungsart",
    status: "Status",
    card: "Kreditkarte",
    paid: "Bezahlt",
    whatNext: "Was möchten Sie als nächstes tun?",
    printReceipt: "Quittung drucken",
    newOrder: "Neue Bestellung starten",
    autoRedirect: "Sie werden in wenigen Sekunden zur Startseite weitergeleitet.",
    thankYou: "Vielen Dank!",
    enjoyMeal: "Wir hoffen, Ihnen schmeckt Ihr Essen.",
  },
};