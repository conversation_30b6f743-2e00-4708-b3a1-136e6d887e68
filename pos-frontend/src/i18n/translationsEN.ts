export const translationsEN = {
  header: "header",
  footer: "footer",
  language: {
    english: "English",
    german: "German",
    french: "French",
    arabic: "Arabic",
    label: "Available Languages",
  },
  theme: {
    label: "Theme",
    light: "Light",
    dark: "Dark",
  },
  api: {
    health: "API Health",
    healthy: "Healthy",
    unreachable: "Unreachable",
    checking: "Checking...",
    healthyDesc: "The API is reachable and working as expected.",
    unreachableDesc: "The API is currently unreachable.",
    checkingDesc: "Checking API health status...",
  },
  welcome: {
    title: "Welcome to Saba POS",
    subtitle: "Touch the screen to start your order",
    startOrder: "Start Order",
  },
  common: {
    loading: "Loading...",
    back: "Back",
  },
  menu: {
    title: "Menu",
    categories: "Categories",
    addToCart: "Add to Cart",
    noItems: "No items found",
    selectCategory: "Select a category",
    priceNotAvailable: "Price not available",
    loadingCategories: "Loading categories...",
    loadingItems: "Loading items...",
  },
  cart: {
    title: "Cart",
    empty: "Your cart is empty",
    total: "Total",
    each: "each",
    remove: "Remove item",
    item: "item",
    items: "items",
    viewCart: "View Cart",
    quantity: "Quantity",
    increaseQuantity: "Increase quantity",
    decreaseQuantity: "Decrease quantity",
  },
  order: {
    summary: "Order Summary",
    subtotal: "Subtotal",
    tax: "Tax",
    total: "Total",
    proceedToPayment: "Proceed to Payment",
    continueShopping: "Continue Shopping",
    backToMenu: "Back to Menu",
    emptyCartPaymentDisabled: "Add items to cart before proceeding to payment",
  },
  payment: {
    title: "Payment",
    amountDue: "Amount Due",
    orderSummary: "Order Summary",
    selectAction: "Select Payment Action",
    simulateSuccess: "Process Payment (Success)",
    simulateFailure: "Simulate Payment Failure",
    processing: "Processing Payment...",
    pleaseWait: "Please wait while we process your payment",
    approved: "Payment Approved",
    declined: "Payment Declined",
    error: "Payment Error",
    ready: "Ready for Payment",
    retry: "Retry Payment",
    backToCart: "Back to Cart",
    redirecting: "Redirecting to confirmation...",
  },
  confirmation: {
    title: "Order Confirmation",
    success: "Payment Successful!",
    message: "Your order has been confirmed and payment processed.",
    orderDetails: "Order Details",
    orderNumber: "Order Number",
    orderTime: "Order Time",
    paymentMethod: "Payment Method",
    status: "Status",
    card: "Credit Card",
    paid: "Paid",
    whatNext: "What would you like to do?",
    printReceipt: "Print Receipt",
    newOrder: "Start New Order",
    autoRedirect: "You will be redirected to the home page in a few seconds.",
    thankYou: "Thank You!",
    enjoyMeal: "We hope you enjoy your meal.",
  },
}; 