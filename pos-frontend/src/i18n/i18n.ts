import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { translationsEN } from './translationsEN';
import { translationsDE } from './translationsDE';
import { translationsFR } from './translationsFR';
import { translationsAR } from './translationsAR';


i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: translationsEN,
      },
      de: {
        translation: translationsDE,
      },
      fr: {
        translation: translationsFR,
      },
      ar: {
        translation: translationsAR,
      },
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n; 