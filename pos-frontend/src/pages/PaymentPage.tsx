import React, { useState } from 'react';
import paymentService from '../lib/PaymentService';
import { useNavigate } from 'react-router-dom';
import { useCartStore } from '../stores/cartStore';
import { usePriceFormat } from '../hooks/usePriceFormat';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { Separator } from '../components/ui/separator';
import { useTranslation } from 'react-i18next';
import { CreditCard, AlertCircle, CheckCircle, Loader2, ArrowLeft } from 'lucide-react';

const PaymentPage: React.FC = () => {
  const { t } = useTranslation();
  const { formatPrice } = usePriceFormat();
  const { total, items, clearCart } = useCartStore();
  const amount = total;
  const currency = items.length > 0 ? items[0]?.priceObj?.currency || '' : '';
  const [status, setStatus] = useState<'idle' | 'processing' | 'success' | 'error' | 'declined'>('idle');
  const [message, setMessage] = useState('');
  const navigate = useNavigate();

  const handlePayment = async (simulateSuccess?: boolean) => {
    setStatus('processing');
    setMessage('');
    try {
      const result = await paymentService.startPayment(amount, simulateSuccess);
      if (result.success) {
        setStatus('success');
        setMessage(result.message);
        setTimeout(() => {
          clearCart();
          navigate('/order-confirmation');
        }, 2000);
      } else {
        setStatus('declined');
        setMessage(result.message);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
      setStatus('error');
      setMessage(errorMessage);
    }
  };

  const handleBack = () => {
    navigate('/order-summary');
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
      case 'declined':
        return <AlertCircle className="h-8 w-8 text-red-500" />;
      default:
        return <CreditCard className="h-8 w-8 text-gray-500" />;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'processing':
        return t('payment.processing');
      case 'success':
        return t('payment.approved');
      case 'declined':
        return t('payment.declined');
      case 'error':
        return t('payment.error');
      default:
        return t('payment.ready');
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'declined':
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <Button
            variant="secondary"
            onClick={handleBack}
            className="flex items-center gap-2"
            disabled={status === 'processing'}
          >
            <ArrowLeft className="h-4 w-4" />
            {t('common.back')}
          </Button>
          <h1 className="text-2xl font-bold text-foreground">{t('payment.title')}</h1>
          <div className="w-20" /> {/* Spacer for centering */}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto p-6 space-y-6">
        {/* Payment Amount Card */}
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              {getStatusIcon()}
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-2">{t('payment.amountDue')}</p>
              <p className="text-4xl font-bold text-foreground">
                {formatPrice(amount, currency)}
              </p>
            </div>
            <div className={`text-lg font-medium ${getStatusColor()}`}>
              {getStatusMessage()}
            </div>
            {message && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700">{message}</p>
              </div>
            )}
          </div>
        </Card>

        {/* Order Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('payment.orderSummary')}</h3>
          <div className="space-y-2">
            {items.map((item, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span>{item.quantity}× {item.item.name}</span>
                <span>{formatPrice(item.subtotal, currency)}</span>
              </div>
            ))}
            <Separator className="my-3" />
            <div className="flex justify-between font-semibold">
              <span>{t('cart.total')}</span>
              <span>{formatPrice(total, currency)}</span>
            </div>
          </div>
        </Card>

        {/* Payment Actions */}
        <Card className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">{t('payment.selectAction')}</h3>
            
            {status === 'idle' && (
              <div className="space-y-3">
                <Button 
                  onClick={() => handlePayment(true)} 
                  className="w-full h-14 text-lg bg-green-600 hover:bg-green-700"
                >
                  <CreditCard className="h-5 w-5 mr-2" />
                  {t('payment.simulateSuccess')}
                </Button>
                <Button 
                  onClick={() => handlePayment(false)} 
                  variant="outline"
                  className="w-full h-14 text-lg border-red-300 text-red-600 hover:bg-red-50"
                >
                  <AlertCircle className="h-5 w-5 mr-2" />
                  {t('payment.simulateFailure')}
                </Button>
              </div>
            )}

            {status === 'processing' && (
              <div className="text-center py-8">
                <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
                <p className="text-lg font-medium text-blue-600">{t('payment.processing')}</p>
                <p className="text-sm text-gray-500 mt-2">{t('payment.pleaseWait')}</p>
              </div>
            )}

            {status === 'success' && (
              <div className="text-center py-8">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <p className="text-xl font-semibold text-green-600 mb-2">{t('payment.approved')}</p>
                <p className="text-sm text-gray-500">{t('payment.redirecting')}</p>
              </div>
            )}

            {(status === 'declined' || status === 'error') && (
              <div className="space-y-4">
                <div className="text-center py-4">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <p className="text-lg font-medium text-red-600 mb-2">
                    {status === 'declined' ? t('payment.declined') : t('payment.error')}
                  </p>
                  <p className="text-sm text-gray-500">{message}</p>
                </div>
                <Button 
                  onClick={() => handlePayment(true)}
                  className="w-full h-14 text-lg bg-blue-600 hover:bg-blue-700"
                >
                  {t('payment.retry')}
                </Button>
                <Button 
                  onClick={handleBack}
                  variant="outline"
                  className="w-full h-12"
                >
                  {t('payment.backToCart')}
                </Button>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PaymentPage;