import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { CheckCircle, Receipt, Home } from 'lucide-react';

const OrderConfirmationPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    // Auto-redirect to welcome page after 10 seconds
    const timer = setTimeout(() => {
      navigate('/');
    }, 10000);

    return () => clearTimeout(timer);
  }, [navigate]);

  const handleNewOrder = () => {
    navigate('/');
  };

  const handlePrintReceipt = () => {
    // Placeholder for printing functionality
    window.print();
  };

  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-foreground text-center">{t('confirmation.title')}</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto p-6 space-y-6">
        {/* Success Message */}
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-green-500" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-green-600 mb-2">{t('confirmation.success')}</h2>
              <p className="text-lg text-gray-600">{t('confirmation.message')}</p>
            </div>
          </div>
        </Card>

        {/* Order Details */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('confirmation.orderDetails')}</h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t('confirmation.orderNumber')}</span>
              <span className="font-mono">#{Math.floor(Math.random() * 10000).toString().padStart(4, '0')}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t('confirmation.orderTime')}</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t('confirmation.paymentMethod')}</span>
              <span>{t('confirmation.card')}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">{t('confirmation.status')}</span>
              <span className="text-green-600 font-medium">{t('confirmation.paid')}</span>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <Card className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">{t('confirmation.whatNext')}</h3>
            
            <div className="space-y-3">
              <Button 
                onClick={handlePrintReceipt}
                variant="outline"
                className="w-full h-14 text-lg"
              >
                <Receipt className="h-5 w-5 mr-2" />
                {t('confirmation.printReceipt')}
              </Button>
              
              <Button 
                onClick={handleNewOrder}
                className="w-full h-14 text-lg bg-blue-600 hover:bg-blue-700"
              >
                <Home className="h-5 w-5 mr-2" />
                {t('confirmation.newOrder')}
              </Button>
            </div>

            <div className="text-center pt-4">
              <p className="text-sm text-gray-500">
                {t('confirmation.autoRedirect')}
              </p>
            </div>
          </div>
        </Card>

        {/* Thank You Message */}
        <Card className="p-6 bg-gradient-to-r from-blue-50 to-green-50">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">{t('confirmation.thankYou')}</h3>
            <p className="text-gray-600">{t('confirmation.enjoyMeal')}</p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
