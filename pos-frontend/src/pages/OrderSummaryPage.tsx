import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useCartStore } from '../stores/cartStore';
import { Button } from '../components/ui/button';
import OrderItemRow from '../components/OrderItemRow';
import OrderTotals from '../components/OrderTotals';

const OrderSummaryPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { items, total, updateQuantity, removeItem } = useCartStore();

  // Extract currency with better fallback handling
  const currency = items.length > 0 ? items[0]?.priceObj?.currency || '' : '';
  const subtotal = total;
  const tax = 0; // TODO: Tax calculation to be implemented in dedicated tax story
  const grandTotal = subtotal + tax;

  const handleProceedToPayment = () => {
    navigate('/payment');
  };

  if (items.length === 0) {
    return (
      <div className="bg-background h-full">
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-foreground mb-4">
              {t('order.summary')}
            </h1>
            <p className="text-muted-foreground mb-6">
              {t('cart.empty')}
            </p>
            <Button onClick={() => navigate('/menu')}>
              {t('order.backToMenu')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background h-full">
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold text-foreground mb-6">
          {t('order.summary')}
        </h1>
      
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <div className="xl:col-span-2">
          <div className="space-y-4">
            {items.map((cartItem) => (
              <OrderItemRow
                key={cartItem.id}
                cartItem={cartItem}
                onUpdateQuantity={updateQuantity}
                onRemoveItem={removeItem}
                isUpdating={false}
              />
            ))}
          </div>
        </div>
        
        <div className="space-y-4">
          <OrderTotals
            subtotal={subtotal}
            tax={tax}
            total={grandTotal}
            currency={currency}
          />
          
          <Button
            className="w-full"
            size="lg"
            onClick={handleProceedToPayment}
            disabled={items.length === 0}
            aria-label={items.length === 0 ? t('order.emptyCartPaymentDisabled') : t('order.proceedToPayment')}
          >
            {t('order.proceedToPayment')}
          </Button>
          
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/menu')}
          >
            {t('order.continueShopping')}
          </Button>
        </div>
      </div>
      </div>
    </div>
  );
};

export default OrderSummaryPage;