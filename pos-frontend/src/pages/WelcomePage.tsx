import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';

const WelcomePage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleStartOrder = () => {
    navigate('/menu');
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-8">
      <div className="text-center space-y-8 max-w-2xl">
        <h1 className="text-6xl font-bold text-primary mb-4">
          {t('welcome.title')}
        </h1>
        
        <p className="text-2xl text-muted-foreground mb-12">
          {t('welcome.subtitle')}
        </p>

        <Button
          onClick={handleStartOrder}
          size="lg"
          className="text-3xl font-semibold py-6 px-12 min-w-[300px] h-auto"
        >
          {t('welcome.startOrder')}
        </Button>
      </div>
    </div>
  );
};

export default WelcomePage;