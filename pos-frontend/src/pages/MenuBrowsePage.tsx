import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useItemGroupChildren } from '../hooks/item-group.hooks';
import { useItems } from '../hooks/item.hooks';
import { useItemPrices } from '../hooks/item-price.hooks';
import { RootItemGroup } from '../model/item-group.model';
import { ItemPrice } from '../model/item-price.model';
import CategoryList from '../components/CategoryList';
import MenuItemCard from '../components/MenuItemCard';
import { Button } from '../components/ui/button';

const MenuBrowsePage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  const { data: itemGroups, isLoading: isLoadingGroups } = useItemGroupChildren(RootItemGroup);
  const { data: items, isLoading: isLoadingItems } = useItems(selectedCategory || undefined);
  const { data: itemPrices } = useItemPrices();

  // Map item prices for quick lookup
  const priceMap = useMemo(() => {
    const map: Record<string, ItemPrice> = {};
    itemPrices?.forEach((price) => {
      if (price.item_code) map[price.item_code] = price;
    });
    return map;
  }, [itemPrices]);

  const handleBackToWelcome = () => {
    navigate('/');
  };


  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
  };

  if (isLoadingGroups) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="text-2xl text-muted-foreground">
            {t('common.loading')}
          </div>
          <div className="text-muted-foreground">
            {t('menu.loadingCategories')}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="secondary"
            onClick={handleBackToWelcome}
          >
            {t('common.back')}
          </Button>
          
          <h1 className="text-4xl font-bold text-primary">
            {t('menu.title')}
          </h1>
          
          <div className="w-24"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <CategoryList 
              categories={itemGroups || []} 
              onCategorySelect={handleCategorySelect}
            />
          </div>
          
          <div className="lg:col-span-3">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {selectedCategory ? (
                isLoadingItems ? (
                  <div className="col-span-full text-center text-muted-foreground text-xl py-12">
                    {t('menu.loadingItems')}
                  </div>
                ) : items && items.length === 0 ? (
                  <div className="col-span-full text-center text-muted-foreground text-xl py-12">
                    {t('menu.noItems')}
                  </div>
                ) : (
                  items?.map((item) => {
                    const priceObj = priceMap[item.name];
                    return (
                      <MenuItemCard
                        key={item.name}
                        item={item}
                        priceObj={priceObj}
                      />
                    );
                  })
                )
              ) : (
                <div className="col-span-full text-center text-muted-foreground text-xl py-12">
                  {t('menu.selectCategory')}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenuBrowsePage;