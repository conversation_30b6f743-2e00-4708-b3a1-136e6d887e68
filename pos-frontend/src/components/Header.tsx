import LanguagePicker from "./LanguagePicker";
import { useCompanies } from "../hooks/company.hooks";

const Header = () => {
  const { data: companies } = useCompanies();

  const company = companies && companies.length > 0 ? companies[0] : null;
  const companyName = company?.company_name || company?.name || "";
  const logoUrl = company?.company_logo ? `http://saba.localhost:8000${company.company_logo}` : "";

  return (
    <header
      className="w-full bg-muted border-b border-muted py-4 px-6 shadow-md text-lg font-semibold text-foreground"
      role="banner"
      aria-label="Header"
      tabIndex={0}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-3">
          {logoUrl && (
            <img
              src={logoUrl}
              alt="Company Logo"
              className="h-8 w-8 object-contain rounded"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
          )}
          <span>
            {companyName || "header"}
          </span>
        </div>
        <LanguagePicker />
      </div>
    </header>
  );
};

export default Header; 