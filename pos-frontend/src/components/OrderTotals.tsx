import { useTranslation } from 'react-i18next';
import { Card } from './ui/card';
import { Separator } from './ui/separator';
import { usePriceFormat } from '../hooks/usePriceFormat';

interface OrderTotalsProps {
  subtotal: number;
  tax: number;
  total: number;
  currency: string;
}

const OrderTotals = ({ subtotal, tax, total, currency }: OrderTotalsProps) => {
  const { t } = useTranslation();
  const { formatPrice } = usePriceFormat();

  return (
    <Card className="p-4 bg-card border-border">
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{t('order.subtotal')}</span>
          <span>{formatPrice(subtotal, currency)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>{t('order.tax')}</span>
          <span>{formatPrice(tax, currency)}</span>
        </div>
        <Separator />
        <div className="flex justify-between text-lg font-semibold">
          <span>{t('order.total')}</span>
          <span className="text-primary">{formatPrice(total, currency)}</span>
        </div>
      </div>
    </Card>
  );
};

export default OrderTotals;