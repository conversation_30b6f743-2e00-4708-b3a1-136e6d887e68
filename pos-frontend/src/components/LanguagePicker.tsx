import { useTranslation } from "react-i18next";
import React from "react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Check } from "lucide-react";
import WorldFlag from "react-world-flags";
import { LANGUAGES } from "@/i18n/languages";

const getAvailableLanguages = () => {
  const stored = localStorage.getItem("availableLanguages");
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return ["en", "de"];
    }
  }
  return ["en", "de"];
};

const LanguagePicker: React.FC = () => {
  const { i18n, t } = useTranslation();
  const currentLanguage = i18n.language as string;
  const [availableLanguages, setAvailableLanguages] = React.useState<string[]>(getAvailableLanguages());

  React.useEffect(() => {
    const onStorage = () => setAvailableLanguages(getAvailableLanguages());
    window.addEventListener("storage", onStorage);
    return () => window.removeEventListener("storage", onStorage);
  }, []);

  const languages = LANGUAGES.filter((l) => availableLanguages.includes(l.code));

  const handleSelectLanguage = (lang: string) => {
    if (lang !== currentLanguage) {
      i18n.changeLanguage(lang);
    }
  };

  const currentLangObj = languages.find((l) => l.code === currentLanguage);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="flex items-center px-2 py-1 rounded-md bg-popover text-popover-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring transition-colors"
          aria-label={t("Select language") as string}
        >
          {currentLangObj && (
            <WorldFlag code={currentLangObj.flagCode} className="w-6 h-6 rounded-sm" aria-label={t(currentLangObj.key)} />
          )}
          <span className="ml-1">{t(currentLangObj?.key || "")}</span>
          <ChevronDown className="ml-2 w-4 h-4" aria-hidden="true" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onSelect={() => handleSelectLanguage(lang.code)}
            className={currentLanguage === lang.code ? "font-bold bg-accent/50" : ""}
            aria-label={t(lang.key) as string}
          >
            <WorldFlag code={lang.flagCode} className="w-6 h-6 rounded-sm" aria-label={t(lang.key)} />
            <span className="ml-2">{t(lang.key)}</span>
            {currentLanguage === lang.code && (
              <Check className="ml-auto w-4 h-4 text-primary" aria-hidden="true" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguagePicker;
