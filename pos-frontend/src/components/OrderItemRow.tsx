import { useTranslation } from 'react-i18next';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { CartItem } from '../stores/cartStore';
import { usePriceFormat } from '../hooks/usePriceFormat';

interface OrderItemRowProps {
  cartItem: CartItem;
  onUpdateQuantity: (id: string, quantity: number) => void;
  onRemoveItem: (id: string) => void;
  isUpdating?: boolean;
}

const OrderItemRow = ({ cartItem, onUpdateQuantity, onRemoveItem, isUpdating = false }: OrderItemRowProps) => {
  const { t } = useTranslation();
  const { formatItemPrice, formatPrice } = usePriceFormat();
  const currency = cartItem.priceObj?.currency || '';

  return (
    <Card className="p-4 bg-card border-border">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-card-foreground truncate">
            {cartItem.item.item_name}
          </h4>
          <p className="text-sm text-muted-foreground">
            {formatItemPrice(cartItem.priceObj)} {t('cart.each')}
          </p>
          {cartItem.customizations.length > 0 && (
            <div className="mt-1">
              {cartItem.customizations.map((custom) => (
                <span key={custom.id} className="text-xs text-muted-foreground block">
                  + {custom.name} (+{formatPrice(custom.price, currency)})
                </span>
              ))}
            </div>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemoveItem(cartItem.id)}
          disabled={isUpdating}
          className="text-destructive hover:text-destructive/80 ml-2 h-6 w-6 p-0"
          aria-label={`${t('cart.remove')} ${cartItem.item.item_name}`}
        >
          ✕
        </Button>
      </div>
      
      <div className="flex items-center justify-between mt-3">
        <div className="flex items-center space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onUpdateQuantity(cartItem.id, cartItem.quantity - 1)}
            disabled={cartItem.quantity <= 1 || isUpdating}
            className="h-8 w-8 p-0"
            aria-label={t('cart.decreaseQuantity')}
          >
            -
          </Button>
          
          <span className="text-sm font-medium w-8 text-center" aria-label={`${t('cart.quantity')}: ${cartItem.quantity}`}>
            {isUpdating ? '...' : cartItem.quantity}
          </span>
          
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onUpdateQuantity(cartItem.id, cartItem.quantity + 1)}
            disabled={isUpdating}
            className="h-8 w-8 p-0"
            aria-label={t('cart.increaseQuantity')}
          >
            +
          </Button>
        </div>
        
        <div className="font-semibold text-primary">
          {formatPrice(cartItem.subtotal, currency)}
        </div>
      </div>
    </Card>
  );
};

export default OrderItemRow;