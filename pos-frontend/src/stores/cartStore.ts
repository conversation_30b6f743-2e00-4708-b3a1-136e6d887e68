import { create } from 'zustand';
import { Item } from '../model/item.model';
import { ItemPrice } from '../model/item-price.model';

export interface CartItem {
  item: Item;
  quantity: number;
  customizations: {
    id: string;
    name: string;
    price: number;
  }[];
  subtotal: number;
  id: string; // unique identifier for this cart item (item + customizations)
  priceObj?: ItemPrice; // price information from price list
}

interface CartState {
  items: CartItem[];
  total: number;
  addItem: (item: Item, quantity: number, customizations?: CartItem['customizations'], priceObj?: ItemPrice) => void;
  removeItem: (cartItemId: string) => void;
  updateQuantity: (cartItemId: string, quantity: number) => void;
  clearCart: () => void;
  getItemCount: () => number;
}

const calculateSubtotal = (_item: Item, quantity: number, customizations: CartItem['customizations'] = [], priceObj?: ItemPrice) => {
  const basePrice = priceObj?.price_list_rate || 0;
  const customizationPrice = customizations.reduce((sum, c) => sum + c.price, 0);
  return (basePrice + customizationPrice) * quantity;
};

const generateCartItemId = (item: Item, customizations: CartItem['customizations'] = []) => {
  const customizationIds = customizations.map(c => c.id).sort().join(',');
  return `${item.name}-${customizationIds}`;
};

export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  total: 0,

  addItem: (item: Item, quantity: number, customizations = [], priceObj) => {
    const cartItemId = generateCartItemId(item, customizations);
    const subtotal = calculateSubtotal(item, quantity, customizations, priceObj);
    
    set((state) => {
      const existingItemIndex = state.items.findIndex(ci => ci.id === cartItemId);
      
      let newItems: CartItem[];
      
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        newItems = [...state.items];
        const existingItem = newItems[existingItemIndex];
        newItems[existingItemIndex] = {
          ...existingItem,
          quantity: existingItem.quantity + quantity,
          subtotal: calculateSubtotal(item, existingItem.quantity + quantity, customizations, priceObj)
        };
      } else {
        // Add new item
        const newCartItem: CartItem = {
          item,
          quantity,
          customizations,
          subtotal,
          id: cartItemId,
          priceObj
        };
        newItems = [...state.items, newCartItem];
      }
      
      const newTotal = newItems.reduce((sum, ci) => sum + ci.subtotal, 0);
      
      return {
        items: newItems,
        total: newTotal
      };
    });
  },

  removeItem: (cartItemId: string) => {
    set((state) => {
      const newItems = state.items.filter(ci => ci.id !== cartItemId);
      const newTotal = newItems.reduce((sum, ci) => sum + ci.subtotal, 0);
      
      return {
        items: newItems,
        total: newTotal
      };
    });
  },

  updateQuantity: (cartItemId: string, quantity: number) => {
    if (quantity <= 0) {
      get().removeItem(cartItemId);
      return;
    }
    
    set((state) => {
      const newItems = state.items.map(ci => {
        if (ci.id === cartItemId) {
          return {
            ...ci,
            quantity,
            subtotal: calculateSubtotal(ci.item, quantity, ci.customizations, ci.priceObj)
          };
        }
        return ci;
      });
      
      const newTotal = newItems.reduce((sum, ci) => sum + ci.subtotal, 0);
      
      return {
        items: newItems,
        total: newTotal
      };
    });
  },

  clearCart: () => {
    set({ items: [], total: 0 });
  },

  getItemCount: () => {
    return get().items.reduce((sum, ci) => sum + ci.quantity, 0);
  }
}));