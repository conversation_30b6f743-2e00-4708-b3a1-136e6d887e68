// Electron API Type Declarations

interface PrintJobData {
  content: string;
  copies?: number;
  [key: string]: unknown;
}

interface PrinterAPI {
  print: (data: PrintJobData) => Promise<{
    success: boolean;
    message: string;
    jobId: string | null;
    error?: string;
  }>;
  getStatus: () => Promise<{
    connected: boolean;
    ready: boolean;
    paper: 'ok' | 'low' | 'out';
    error: string | null;
  }>;
}

interface PaymentTerminalAPI {
  processPayment: (amount: number) => Promise<{
    success: boolean;
    transactionId: string | null;
    amount: number;
    cardLast4: string;
    cardType: string;
    approvalCode: string;
    error?: string;
  }>;
  getStatus: () => Promise<{
    connected: boolean;
    ready: boolean;
    cardPresent: boolean;
    error: string | null;
  }>;
}

interface ElectronAPI {
  printer: PrinterAPI;
  paymentTerminal: PaymentTerminalAPI;
  platform: string;
  version?: string;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};