import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { ItemGroup } from "../model/item-group.model";

export const useHealth = () => {
    return useQuery({
        queryKey: ["api-health"],
        queryFn: () => axios.get("/health"),
        refetchInterval: 10000,
    });
};

export const useItemGroups = () => {
    return useQuery({
        queryKey: ["item-groups"],
        queryFn: async () => {
            const res = await axios.get<ItemGroup[]>("/item-groups");
            return res.data;
        },
        refetchInterval: 10000,
    });
};

export const useItemGroupChildren = (parentName?: string | null) => {
    return useQuery({
        queryKey: ["item-group-children", parentName],
        queryFn: async () => {
            const res = await axios.get<ItemGroup[]>(`/item-groups/${parentName}/children`);
            return res.data;
        },
        enabled: !!parentName,
        refetchInterval: 10000,
    });
};