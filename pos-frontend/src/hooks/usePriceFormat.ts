import { ItemPrice } from '../model/item-price.model';

export const usePriceFormat = () => {
  const formatPrice = (
    amount: number, 
    currency: string = '', 
    showCurrency: boolean = true, 
    decimals: number = 2
  ): string => {
    const formattedAmount = amount.toFixed(decimals);
    
    return showCurrency && currency 
      ? `${formattedAmount} ${currency}`
      : formattedAmount;
  };

  const formatItemPrice = (
    priceObj: ItemPrice | null | undefined, 
    showCurrency: boolean = true, 
    decimals: number = 2
  ): string => {
    const price = priceObj?.price_list_rate || 0;
    const currency = priceObj?.currency || '';
    return formatPrice(price, currency, showCurrency, decimals);
  };

  return {
    formatPrice,
    formatItemPrice
  };
};

export default usePriceFormat;