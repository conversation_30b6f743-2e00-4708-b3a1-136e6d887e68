import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { Item } from "../model/item.model";

export const useItems = (itemGroup?: string) => {
    return useQuery({
        queryKey: ["items", itemGroup],
        queryFn: async () => {
            const response = await axios.get<Item[]>(`/items${itemGroup ? `?item_group=${itemGroup}` : ''}`);
            return response.data;
        },
        refetchInterval: 10000,
        enabled: !!itemGroup,
    });
};