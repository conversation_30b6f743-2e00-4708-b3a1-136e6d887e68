# Saba Restaurant Management System

A comprehensive restaurant management system built with microservices architecture, featuring pickup and kitchen panels, POS frontend, REST API service, and MongoDB database.

## System Architecture

The system consists of the following microservices:

- **Pickup Panel** (Port: 8081)
  - Handles order pickup management
  - Real-time order status updates
  - Customer interaction interface

- **POS Frontend** (Port: 8082)
  - Point of Sale interface for staff
  - Order creation and management
  - Payment processing

- **Kitchen Panel** (Port: 8083)
  - Kitchen order management
  - Order preparation tracking
  - Kitchen staff interface

- **API Service** (Port: 3000)
  - RESTful API endpoints
  - Business logic handling
  - Data validation and processing
  - Service coordination

- **MongoDB Database** (Port: 27017)
  - Document-based database
  - Stores all application data
  - Persistent data storage

## Prerequisites

- Docker
- Docker Compose
- Node.js (for local development)

## Getting Started

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd saba
   ```

2. Start the services:
   ```bash
   docker-compose up -d
   ```

3. Access the services:
   - Pickup Panel: http://localhost:8081
   - POS Frontend: http://localhost:8082
   - Kitchen Panel: http://localhost:8083
   - API Service: http://localhost:3000
   - MongoDB: mongodb://localhost:27017

## Development

### Project Structure
```
saba/
├── pickup-panel/     # Pickup management interface
├── pos-frontend/     # Point of Sale frontend
├── kitchen-panel/    # Kitchen management interface
├── api/              # REST API service
└── docker-compose.yaml
```

### Environment Configuration

The system uses the following default credentials:

- MongoDB:
  - Username: user
  - Password: password
  - Database: saba

### Development Workflow

1. Each service runs in development mode with hot-reload enabled
2. Code changes in the respective directories are automatically reflected
3. Services are connected through the `saba-network` Docker network

## Service Details

### Pickup Panel
- Handles customer order pickup
- Real-time order status updates
- Customer notification system

### POS Frontend
- Point of Sale interface
- Order creation and management
- Payment processing

### Kitchen Panel
- Order preparation management
- Kitchen staff interface
- Order status updates

### API Service
- RESTful API endpoints
- Business logic implementation
- Data validation and processing
- Service coordination
- Authentication and authorization

### MongoDB
- Document database
- Persistent storage
- Data backup and recovery